{% include 'html.twig' %}

{% block body %}
{% include 'component/sidebar.twig' %}
{% include 'customer/modal.twig' %}

<style>
    @media (max-width: 575px) {
        .mobile-pagination {
            flex-direction: column;
            align-items: flex-start;
        }
        .pagination {
            margin-top: 10px;
        }

        .card-header .row {
            display: flex;
            flex-direction: column !important;
            justify-content: center;
        }

        .card-header .card-title {
            text-align: center;
        }

        .card-header .btn-primary {
            margin-top: 10px;
        }
    }
</style>

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row pt-4">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-body-tertiary">
                            <div class="row align-items-center">
                                <div class="col">                      
                                    <h4 class="card-title mb-0">Customers</h4>                      
                                </div>
                                <div class="col-auto">                      
                                    <div class="d-flex gap-2">
{#                                         <form action="" method="GET" class="d-flex gap-2">
                                            <input type="hidden" name="per_page" value="{{ perPage }}">
                                            <input type="text" name="search" class="form-control" placeholder="Ara..." value="{{ search }}">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i>
                                            </button>
                                        </form> #}
                                        <button class="btn btn-primary rounded-pill px-4" data-bs-toggle="modal" data-bs-target="#createCustomerModal">
                                            <i class="fas fa-plus me-2"></i> Add Customer
                                        </button>
                                    </div>
                                </div>
                            </div>                                  
                        </div>
                        <div class="card-body p-4">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th class="border-top-0">Name</th>
                                            <th class="border-top-0">Company</th>
                                            <th class="border-top-0">Phone</th>
                                            <th class="border-top-0">House Number</th>
                                            <th class="border-top-0">City</th>
                                            <th class="border-top-0">Country</th>
                                            <th class="border-top-0">Zip Code</th>
                                            <th class="border-top-0">Authorized User</th>
                                            <th class="border-top-0 ">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer in customers %}
                                        <tr>
                                            <td>{{ customer.first_name }} {{ customer.last_name }}</td>
                                            <td>{{ customer.company }}</td>
                                            <td>{{ customer.phone }}</td>
                                            <td>
                                                {% if customer.house_number %}
                                                    {{ customer.house_number }}
                                                {% else %}
                                                    <span data-bs-toggle="tooltip" data-bs-placement="top" title="House number not provided">
                                                        <i class="fas fa-exclamation-triangle text-danger"></i>
                                                    </span>
                                                {% endif %}
                                            </td>
                                            <td>{{ customer.city }}</td>
                                            <td>{{ customer.country }}</td>
                                            <td>{{ customer.zip_code }}</td>
                                            <td>
                                                {% if authUser.role == 'admin' %}
                                                    <a href="#" class="assign-user" data-bs-toggle="modal" data-bs-target="#assignUserModal" data-customer-id="{{ customer.id }}" data-current-user="{{ customer.yetki_id }}">
                                                        {% if customer.authorized_user %}
                                                            {{ customer.authorized_user.name|split(' ')|first }}
                                                        {% else %}
                                                            <span class="text-muted">Assign user</span>
                                                        {% endif %}
                                                    </a>
                                                {% else %}
                                                    {% if customer.authorized_user %}
                                                        {{ customer.authorized_user.name|split(' ')|first }}
                                                    {% endif %}
                                                {% endif %}
                                            </td>
                                            <td class="text-end">
                                                <a href="{{ route('customer.edit', {'id': customer.id}) }}"><i class="fas fa-pen text-secondary fs-18"></i></a>
                                                <a href="#" class="delete-customer" data-id="{{ customer.id }}"><i class="fas fa-trash-alt text-secondary fs-18"></i></a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-3 mobile-pagination" style="flex-wrap: wrap;">
                                <div class="d-flex align-items-center">
                                    <label class="me-2">Sayfa başına:</label>
                                    <select class="form-select form-select-sm" style="width: auto;" onchange="window.location.href='?per_page=' + this.value + '{% if search %}&search={{ search }}{% endif %}'">
                                        <option value="20" {% if perPage == 20 %}selected{% endif %}>20</option>
                                        <option value="50" {% if perPage == 50 %}selected{% endif %}>50</option>
                                        <option value="100" {% if perPage == 100 %}selected{% endif %}>100</option>
                                    </select>
                                </div>
                                <div>
                                    <nav>
                                        <ul class="pagination mb-0">
                                            <li class="page-item {% if customers.currentPage == 1 %}disabled{% endif %}">
                                                <a class="page-link" href="?page={{ customers.currentPage - 1 }}&per_page={{ perPage }}{% if search %}&search={{ search }}{% endif %}">Önceki</a>
                                            </li>
                                            
                                            {% for i in range(1, customers.lastPage) %}
                                                <li class="page-item {% if customers.currentPage == i %}active{% endif %}">
                                                    <a class="page-link" href="?page={{ i }}&per_page={{ perPage }}{% if search %}&search={{ search }}{% endif %}">{{ i }}</a>
                                                </li>
                                            {% endfor %}
                                            
                                            <li class="page-item {% if customers.currentPage == customers.lastPage %}disabled{% endif %}">
                                                <a class="page-link" href="?page={{ customers.currentPage + 1 }}&per_page={{ perPage }}{% if search %}&search={{ search }}{% endif %}">Sonraki</a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // SevDesk iletişim yollarını saklamak için global değişken
    let communicationWays = [];
    
    // Add Customer butonuna tıklandığında SevDesk API'sinden verileri çek
    $('[data-bs-target="#createCustomerModal"]').on('click', function(e) {
        // Modal açılmadan önce API'ye istek at
        $.ajax({
            url: '{{ route("customer.communication-ways") }}',
            method: 'GET',
            beforeSend: function() {
                // İsteği göndermeden önce loading göster
                $('#createCustomerModal .modal-content').append('<div class="position-absolute w-100 h-100 d-flex justify-content-center align-items-center bg-white bg-opacity-75" id="loadingOverlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
            },
            success: function(response) {
                if (response.success && response.data) {
                    // İletişim yollarını global değişkene kaydet
                    communicationWays = response.data;
                    console.log('Loaded communication ways:', communicationWays.length);
                }
            },
            error: function(xhr) {
                console.error('Error loading communication ways:', xhr);
                alert('Failed to load data from SevDesk. Please try again.');
            },
            complete: function() {
                // İstek tamamlandığında loading'i kaldır
                $('#loadingOverlay').remove();
            }
        });
    });
    
    // Email alanına yazıldığında otomatik tamamlama
    $(document).on('keyup', '#customerEmail', function() {
        const inputValue = $(this).val().trim().toLowerCase();
        
        // En az 5 karakter yazılmışsa arama yap
        if (inputValue.length < 5) {
            $('.email-autocomplete-dropdown').remove();
            return;
        }
        
        // Eşleşen emailleri bul
        const matches = communicationWays.filter(item => 
            item.value && item.value.toLowerCase().includes(inputValue)
        );
        
        // Mevcut dropdown'ı kaldır
        $('.email-autocomplete-dropdown').remove();
        
        // Eşleşme yoksa çık
        if (matches.length === 0) {
            return;
        }
        
        // Dropdown oluştur
        const $input = $(this);
        const $dropdown = $('<div class="email-autocomplete-dropdown"></div>');
        
        // Tema kontrolü
        const isDarkMode = document.documentElement.getAttribute('data-bs-theme') === 'dark';
        
        // Dropdown stilini ayarla
        $dropdown.css({
            position: 'absolute',
            top: $input.offset().top + $input.outerHeight(),
            left: $input.offset().left,
            width: $input.outerWidth(),
            maxHeight: '200px',
            overflowY: 'auto',
            backgroundColor: isDarkMode ? '#2b2b2b' : 'white',
            border: `1px solid ${isDarkMode ? '#404040' : '#ccc'}`,
            borderRadius: '4px',
            zIndex: 9999,
            boxShadow: isDarkMode ? '0 2px 5px rgba(0, 0, 0, 0.4)' : '0 2px 5px rgba(0, 0, 0, 0.2)'
        });
        
        // Eşleşen emailleri dropdown'a ekle
        matches.slice(0, 10).forEach(item => {
            const $item = $('<div class="email-autocomplete-item"></div>');
            $item.css({
                padding: '8px 12px',
                cursor: 'pointer',
                borderBottom: `1px solid ${isDarkMode ? '#404040' : '#eee'}`,
                color: isDarkMode ? '#e9ecef' : '#000000', // Tema bazlı renk
                fontWeight: '500',
                backgroundColor: isDarkMode ? '#2b2b2b' : 'white'
            });
            
            $item.text(item.value);
            
            // Hover efekti
            $item.hover(
                function() { 
                    $(this).css({
                        'backgroundColor': isDarkMode ? '#404040' : '#f0f0f0',
                        'color': isDarkMode ? '#ffffff' : '#000000'
                    }); 
                },
                function() { 
                    $(this).css({
                        'backgroundColor': isDarkMode ? '#2b2b2b' : 'white',
                        'color': isDarkMode ? '#e9ecef' : '#000000'
                    }); 
                }
            );
            
            // Tıklama olayı
            $item.on('click', function() {
                $input.val(item.value);
                
                // Contact ID'yi al ve kaydet
                const contactId = item.contact.id;
                $('#clientId').val(contactId);
                
                // Client ID'yi görüntüle
                $('#clientIdDisplay').val(contactId);
                $('.client-id-display').show();
                
                $dropdown.remove();
            });
            
            $dropdown.append($item);
        });
        
        // Dropdown'ı sayfaya ekle
        $('body').append($dropdown);
    });
    
    // Sayfa herhangi bir yerine tıklandığında dropdown'ı kapat
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#customerEmail, .email-autocomplete-dropdown').length) {
            $('.email-autocomplete-dropdown').remove();
        }
    });
    
    // Save Customer
    $('#saveCustomerBtn').click(function(e) {
        e.preventDefault();
        
        // Form validation
        if (!$('#createCustomerForm')[0].checkValidity()) {
            $('#createCustomerForm')[0].reportValidity();
            return;
        }
        
        const formData = $('#createCustomerForm').serialize();
        
        $.ajax({
            url: '{{ route("customer.store") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val()
            },
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#createCustomerModal').modal('hide');
                    window.location.reload();
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Unknown error';
                alert('Error creating customer: ' + message);
                console.error('Error:', xhr);
            }
        });
    });

    // Delete Customer
    $('.delete-customer').click(function(e) {
        e.preventDefault();
        if (confirm('Are you sure you want to delete this customer?')) {
            const customerId = $(this).data('id');
            
            $.ajax({
                url: '/customer/' + customerId,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        window.location.reload();
                    }
                },
                error: function(xhr) {
                    alert('Error deleting customer: ' + (xhr.responseJSON?.message || 'Unknown error'));
                }
            });
        }
    });
    
    // Assign User Modal
    $('.assign-user').click(function() {
        const customerId = $(this).data('customer-id');
        const currentUserId = $(this).data('current-user');
        
        $('#assignCustomerId').val(customerId);
        $('#userId').val(currentUserId);
    });
    
    // Save User Assignment
    $('#saveAssignUserBtn').click(function() {
        const formData = $('#assignUserForm').serialize();
        const customerId = $('#assignCustomerId').val();
        
        $.ajax({
            url: '{{ route("customer.assign-user") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val()
            },
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#assignUserModal').modal('hide');
                    window.location.reload();
                }
            },
            error: function(xhr) {
                alert('Error assigning user: ' + (xhr.responseJSON?.message || 'Unknown error'));
            }
        });
    });
});
</script>
{% endblock %}





