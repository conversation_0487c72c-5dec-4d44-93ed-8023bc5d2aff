{% include 'html.twig' %}

{% block body %}
{% include 'component/sidebar.twig' %}

<style>
    .email-autocomplete-dropdown {
        position: absolute;
        max-height: 200px;
        overflow-y: auto;
        z-index: 9999;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    .email-autocomplete-item {
        padding: 8px 12px;
        cursor: pointer;
    }
    
    .email-autocomplete-item:hover {
        background-color: #f0f0f0;
    }
    
    [data-bs-theme="dark"] .email-autocomplete-item:hover {
        background-color: #404040;
        color: #ffffff;
    }
</style>

<div class="page-wrapper">
    <div class="page-content px-0">
        <div class="container-fluid px-4">
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">                      
                                    <h4 class="card-title">Edit Customer</h4>                      
                                </div>
                                <div class="col-auto">                      
                                    <a href="{{ route('customer.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i> Back to List
                                    </a>
                                </div>
                            </div>                                  
                        </div>
                        <div class="card-body">
                            <form id="editCustomerForm">
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">First Name</label>
                                            <input type="text" class="form-control" name="first_name" value="{{ customer.first_name }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Last Name</label>
                                            <input type="text" class="form-control" name="last_name" value="{{ customer.last_name }}" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Email</label>
                                            <input type="email" class="form-control" name="email" value="{{ customer.email }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">SevDesk Client ID</label>
                                            <input type="text" class="form-control" id="clientIdDisplay" value="{{ customer.client_id }}" readonly>
                                            <input type="hidden" name="client_id" id="clientId" value="{{ customer.client_id }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Phone</label>
                                            <input type="text" class="form-control" name="phone" value="{{ customer.phone }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Whatsapp Number</label>
                                            <input type="text" class="form-control" name="whatsapp_number" value="{{ customer.whatsapp_number }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Notification Email</label>
                                            <input type="email" class="form-control" name="notification_email" value="{{ customer.notification_email }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Company</label>
                                    <input type="text" class="form-control" name="company" value="{{ customer.company }}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Address</label>
                                    <textarea class="form-control" name="address" rows="3">{{ customer.address }}</textarea>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">House Number</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="house_number" value="{{ customer.house_number }}">
                                                {% if not customer.house_number %}
                                                <div class="input-group-text bg-warning text-white" data-bs-toggle="tooltip" data-bs-placement="top" title="House number not provided">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Zip Code</label>
                                            <input type="text" class="form-control" name="zip_code" value="{{ customer.zip_code }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">City</label>
                                            <input type="text" class="form-control" name="city" value="{{ customer.city }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Country</label>
                                            <input type="text" class="form-control" name="country" value="{{ customer.country }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">IBAN</label>
                                            <input type="text" class="form-control" name="iban" value="{{ customer.iban }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-3">
                                    <button type="button" class="btn btn-secondary me-2" onclick="window.location.href='{{ route('customer.index') }}'">Cancel</button>
                                    <button type="button" class="btn btn-primary" id="updateCustomerBtn">Update Customer</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // SevDesk iletişim yollarını saklamak için global değişken
    let communicationWays = [];
    
    // Sayfa yüklendiğinde SevDesk API'sinden verileri çek
    $.ajax({
        url: '{{ route("customer.communication-ways") }}',
        method: 'GET',
        beforeSend: function() {
            // İsteği göndermeden önce loading göster
            $('.card-body').append('<div class="position-absolute w-100 h-100 d-flex justify-content-center align-items-center bg-white bg-opacity-75" id="loadingOverlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
        },
        success: function(response) {
            if (response.success && response.data) {
                // İletişim yollarını global değişkene kaydet
                communicationWays = response.data;
                console.log('Loaded communication ways:', communicationWays.length);
            }
        },
        error: function(xhr) {
            console.error('Error loading communication ways:', xhr);
            alert('Failed to load data from SevDesk. Please try again.');
        },
        complete: function() {
            // İstek tamamlandığında loading'i kaldır
            $('#loadingOverlay').remove();
        }
    });
    
    // Email alanına yazıldığında otomatik tamamlama
    $('input[name="email"]').on('keyup', function() {
        const inputValue = $(this).val().trim().toLowerCase();
        
        // En az 5 karakter yazılmışsa arama yap
        if (inputValue.length < 5) {
            $('.email-autocomplete-dropdown').remove();
            return;
        }
        
        // Eşleşen emailleri bul
        const matches = communicationWays.filter(item => 
            item.value && item.value.toLowerCase().includes(inputValue)
        );
        
        // Mevcut dropdown'ı kaldır
        $('.email-autocomplete-dropdown').remove();
        
        // Eşleşme yoksa çık
        if (matches.length === 0) {
            return;
        }
        
        // Dropdown oluştur
        const $input = $(this);
        const $dropdown = $('<div class="email-autocomplete-dropdown"></div>');
        
        // Tema kontrolü
        const isDarkMode = document.documentElement.getAttribute('data-bs-theme') === 'dark';
        
        // Dropdown stilini ayarla
        $dropdown.css({
            position: 'absolute',
            top: $input.offset().top + $input.outerHeight(),
            left: $input.offset().left,
            width: $input.outerWidth(),
            maxHeight: '200px',
            overflowY: 'auto',
            backgroundColor: isDarkMode ? '#2b2b2b' : 'white',
            border: `1px solid ${isDarkMode ? '#404040' : '#ccc'}`,
            borderRadius: '4px',
            zIndex: 9999,
            boxShadow: isDarkMode ? '0 2px 5px rgba(0, 0, 0, 0.4)' : '0 2px 5px rgba(0, 0, 0, 0.2)'
        });
        
        // Eşleşen emailleri dropdown'a ekle
        matches.slice(0, 10).forEach(item => {
            const $item = $('<div class="email-autocomplete-item"></div>');
            $item.css({
                padding: '8px 12px',
                cursor: 'pointer',
                borderBottom: `1px solid ${isDarkMode ? '#404040' : '#eee'}`,
                color: isDarkMode ? '#e9ecef' : '#000000',
                fontWeight: '500',
                backgroundColor: isDarkMode ? '#2b2b2b' : 'white'
            });
            
            $item.text(item.value);
            
            // Hover efekti
            $item.hover(
                function() { 
                    $(this).css({
                        'backgroundColor': isDarkMode ? '#404040' : '#f0f0f0',
                        'color': isDarkMode ? '#ffffff' : '#000000'
                    }); 
                },
                function() { 
                    $(this).css({
                        'backgroundColor': isDarkMode ? '#2b2b2b' : 'white',
                        'color': isDarkMode ? '#e9ecef' : '#000000'
                    }); 
                }
            );
            
            // Tıklama olayı
            $item.on('click', function() {
                $input.val(item.value);
                
                // Contact ID'yi al ve kaydet
                const contactId = item.contact.id;
                $('#clientId').val(contactId);
                $('#clientIdDisplay').val(contactId);
                
                $dropdown.remove();
            });
            
            $dropdown.append($item);
        });
        
        // Dropdown'ı sayfaya ekle
        $('body').append($dropdown);
    });
    
    // Sayfa herhangi bir yerine tıklandığında dropdown'ı kapat
    $(document).on('click', function(e) {
        if (!$(e.target).closest('input[name="email"], .email-autocomplete-dropdown').length) {
            $('.email-autocomplete-dropdown').remove();
        }
    });
    
    $('#updateCustomerBtn').click(function(e) {
        e.preventDefault();
        
        // Form validation
        if (!$('#editCustomerForm')[0].checkValidity()) {
            $('#editCustomerForm')[0].reportValidity();
            return;
        }
        
        const formData = $('#editCustomerForm').serialize();
        
        $.ajax({
            url: '{{ route("customer.update", {"id": customer.id}) }}',
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val()
            },
            data: formData,
            success: function(response) {
                if (response.success) {
                    window.location.href = '{{ route("customer.index") }}';
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Unknown error';
                alert('Error updating customer: ' + message);
                console.error('Error:', xhr);
            }
        });
    });
});
</script>
{% endblock %}




