<!-- Excel Import Info Modal -->
<div class="modal fade" id="excelInfoModal" tabindex="-1" aria-labelledby="excelInfoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="excelInfoModalLabel">Excel Import Format</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info" role="alert">
                    <h6 class="alert-heading mb-2"><i class="fas fa-info-circle me-2"></i>File Format Information</h6>
                    <p class="mb-2">You can upload Excel (.xlsx, .xls) files. Your file should be in the following format:</p>
                    
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered bg-white mb-2">
                            <thead class="table-light">
                                <tr>
                                    <th>productNumber</th>
                                    <th>quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>PH.85</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>PH.99</td>
                                    <td>5</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <ul class="mb-0 ps-3">
                        <li>The column names must be exactly as shown above: <strong>productNumber</strong> and <strong>quantity</strong></li>
                        <li>Product numbers must exist in the system</li>
                        <li>Quantity must be a positive number</li>
                    </ul>
                </div>
                
                <div class="mt-3">
                    <h6>Sample Excel File</h6>
                    <p>You can download a sample Excel file to use as a template:</p>
                    <a href="#" class="btn btn-sm btn-outline-primary" id="downloadSampleBtn">
                        <i class="fas fa-download me-1"></i> Download Sample
                    </a>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sample Excel file download
    document.getElementById('downloadSampleBtn').addEventListener('click', function(e) {
        e.preventDefault();
        
        // Create a workbook with a worksheet
        const wb = XLSX.utils.book_new();
        const ws_data = [
            ['productNumber', 'quantity'],
            ['PH.85', 10],
            ['PH.99', 5]
        ];
        const ws = XLSX.utils.aoa_to_sheet(ws_data);
        
        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Sample');
        
        // Generate the file and trigger download
        XLSX.writeFile(wb, 'inbound_sample.xlsx');
    });
});
</script>