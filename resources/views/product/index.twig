{% include 'html.twig' %}

{% block body %}
{% include 'component/sidebar.twig' %}

<style>
@media (max-width: 600px) {

    .card-header .row {
        display: flex;
        flex-direction: column !important;
        justify-content: center;
    }

    .card-header .card-title {
        text-align: center;
    }

    .card-header .col-auto {
        margin-top: 10px;
    }
}

.button-cell .btn-primary {
    margin-bottom: 8px;
    width: 100%;
}

.button-cell .btn-warning {
    width: 100%;
}
</style>

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <!-- Notification area -->
            <div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
            
            <div class="row pt-4">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-body-tertiary">
                            <div class="row align-items-center">
                                <div class="col">                      
                                    <h4 class="card-title mb-0">Products</h4>                      
                                </div>
                                <div class="col-auto">                      
                                    <button class="btn btn-primary rounded-pill px-4" data-bs-toggle="modal" data-bs-target="#productModal">
                                        <i class="fas fa-plus me-2"></i> Add Product
                                    </button>
                                    <a href="{{ url('/product-archives') }}" class="btn btn-secondary rounded-pill px-4 ms-2">
                                        <i class="fas fa-archive me-2"></i> Show Archives
                                    </a>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="{{ url('/products') }}" class="btn btn-sm brand-btn {{ selectedBrand is empty and selectedBrand != 'others' ? 'btn-primary' : 'btn-outline-primary' }}">
                                        All
                                    </a>
                                    {% for brand in brands %}
                                        {% if brand is not empty %}
                                        <a href="{{ url('/products?brand=' ~ brand) }}" 
                                           class="btn btn-sm {{ selectedBrand == brand ? 'btn-primary' : 'btn-outline-primary' }} d-flex align-items-center">
                                            {% if file_exists('images/products/' ~ brand ~ '_logo.png') %}
                                                <img src="{{ asset('images/products/' ~ brand ~ '_logo.png') }}" alt="{{ brand }}" height="20" class="me-1">
                                                <span class="d-none d-md-inline"></span>
                                            {% else %}
                                                {{ brand }}
                                            {% endif %}
                                        </a>
                                        {% endif %}
                                    {% endfor %}
                                    <a href="{{ url('/products?brand=others') }}" 
                                       class="btn btn-sm {{ selectedBrand == 'others' ? 'btn-primary' : 'btn-outline-primary' }}">
                                        Others
                                    </a>
                                </div>
                            </div>                                  
                        </div>
                        <div class="card-body p-4">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Item Number</th>
                                            <th>Description</th>
                                            <th>Price</th>
                                            <th>Stock</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in products %}
                                        {% set data = product.decoded_json[0] %}
                                        <tr data-nurederm-id="{{ product.nurederm_id }}">
                                            <td class="product-item-cell">{{ data.itemNumber }}</td>
                                            <td>{{ data.description }}</td>
                                            <td>€{{ data.priceGross|number_format(2, ',', '.') }}</td>
                                            <td class="stock-cell" data-item-number="{{ data.itemNumber }}">
                                                <span class="spinner-border spinner-border-sm" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </span>
                                            </td>
                                            <td class="button-cell">
                                                <button class="btn btn-sm btn-primary edit-product" 
                                                        data-product="{{ product.decoded_json[0]|json_encode|escape('html_attr') }}"
                                                        data-id="{{ product.id }}">
                                                    Edit
                                                </button>
                                                <button class="btn btn-sm btn-warning archive-product" 
                                                        data-id="{{ product.id }}">
                                                    Archive
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Product Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Product Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="productForm">
                    <input type="hidden" id="productId">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Item Number</label>
                            <div class="input-group">
                                <input type="text" class="form-control" name="itemNumber" required>
                                <div class="invalid-feedback">
                                    This Item Number already exists.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">VAT Kind
                              <small class="text-muted ms-1">(ex: 0, 7, 19)</small>
                            </label>
                            <input type="number" class="form-control integer-input" name="vatKind" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Sales Unit</label>
                            <input type="number" class="form-control integer-input" name="salesUnit" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Product Group</label>
                            <input type="text" class="form-control" name="productGroup" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Width</label>
                            <input type="number" class="form-control decimal-input" name="width" required min="0" step="0.1">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Height</label>
                            <input type="number" class="form-control decimal-input" name="height" required min="0" step="0.1">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Length</label>
                            <input type="number" class="form-control decimal-input" name="length1" required min="0" step="0.1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">EAN</label>
                            <input type="text" class="form-control" name="ean" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Weight</label>
                            <input type="number" class="form-control decimal-input" name="weight" required min="0" step="0.1">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Country</label>
                            <input type="text" class="form-control" name="country" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Description</label>
                            <input type="text" class="form-control" name="description" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Tariff Number</label>
                            <input type="text" class="form-control" name="tariffNumber" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Lot No Required</label>
                            <select class="form-control" name="lotNoRequired">
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Purchase Price</label>
                            <input type="number" class="form-control price-input" name="purchasePrice" required min="0" step="0.01">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Price Gross</label>
                            <input type="number" class="form-control price-input" name="priceGross" required min="0" step="0.01">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Price Net</label>
                            <input type="number" class="form-control price-input" name="priceNet" required min="0" step="0.01">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nali Nili</label>
                            <input type="text" class="form-control" name="naliNili" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Reorder Level</label>
                            <input type="number" class="form-control integer-input" name="reorderLevel" required min="0">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Expiry Date</label>
                            <input type="date" class="form-control" name="expiryDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Dispatch Stop</label>
                            <input type="number" class="form-control integer-input" name="dispatchStop" required min="0">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveProduct">Save</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/css/lightbox.min.css" rel="stylesheet">
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.3/js/lightbox.min.js"></script>
<script>
$(document).ready(function () {
    function toFloat(val, decimal = 1) {
        let num = parseFloat(val);
        return isNaN(num) ? 0.0 : parseFloat(num.toFixed(decimal));
    }

    function toInt(val) {
        let num = parseInt(val);
        return isNaN(num) ? 0 : num;
    }

    function toBool(val) {
        return val === "true";
    }

    function toStr(val) {
        return val ? String(val).trim() : "";
    }

    $('.decimal-input').on('blur', function() {
        const val = toFloat(this.value);
        $(this).val(val.toFixed(1));
    });

    $('.price-input').on('blur', function() {
        const val = toFloat(this.value, 2);
        $(this).val(val.toFixed(2));
    });

    $('.integer-input').on('blur', function() {
        const val = toInt(this.value);
        $(this).val(val);
    });

    function smartFloat(val, decimal = 2) {
        let floatVal = parseFloat(val);
        if (Number.isNaN(floatVal)) return 0.0;
        if (Number.isInteger(floatVal)) {
            // Daha büyük bir epsilon değeri kullan
            floatVal += decimal === 1 ? 0.1 : 0.01;
        }
        return parseFloat(floatVal.toFixed(decimal));
    }

    // Item Number kontrolü
    let itemNumberTimer;
    $('input[name="itemNumber"]').on('blur', function() {
        const itemNumber = $(this).val().trim();
        const inputField = $(this);
        
        if (!itemNumber) return;
        
        // Eğer edit modundaysa ve orijinal item number değişmediyse kontrol etme
        const productId = $('#productId').val();
        const originalItemNumber = inputField.data('original-value');
        if (productId && itemNumber === originalItemNumber) return;
        
        $.ajax({
            url: '{{ route("product.checkItemNumber") }}',
            method: 'POST',
            data: { itemNumber: itemNumber },
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.exists) {
                    alert('This Item Number already exists. Please choose a different one.');
                    inputField.val('').focus();
                }
            }
        });
    });

    // Edit product handler
    $('.edit-product').click(function() {
        const productData = $(this).data('product'); // JSON.parse'ı kaldırdık
        const productId = $(this).data('id');
        
        console.log('Product ID:', productId);
        console.log('Product Data:', productData);
        
        // Set product ID
        $('#productId').val(productId);
        
        // Fill form fields
        const form = $('#productForm')[0];
        form.elements['itemNumber'].value = productData.itemNumber || '';
        $(form.elements['itemNumber']).data('original-value', productData.itemNumber || '');
        form.elements['vatKind'].value = productData.vatKind || 0;
        form.elements['salesUnit'].value = productData.salesUnit || 0;
        form.elements['productGroup'].value = productData.productGroup || '';
        form.elements['width'].value = productData.width?.toFixed(1) || '0.0';
        form.elements['height'].value = productData.height?.toFixed(1) || '0.0';
        form.elements['length1'].value = productData.length?.toFixed(1) || '0.0';
        form.elements['ean'].value = productData.ean || '';
        form.elements['weight'].value = productData.weight?.toFixed(1) || '0.0';
        form.elements['country'].value = productData.country || '';
        form.elements['description'].value = productData.description || '';
        form.elements['tariffNumber'].value = productData.tariffNumber || '';
        form.elements['lotNoRequired'].value = productData.lotNoRequired?.toString() || 'false';
        form.elements['purchasePrice'].value = productData.purchasePrice?.toFixed(2) || '0.00';
        form.elements['priceGross'].value = productData.priceGross?.toFixed(2) || '0.00';
        form.elements['priceNet'].value = productData.priceNet?.toFixed(2) || '0.00';
        form.elements['naliNili'].value = productData.naliNili || '';
        form.elements['reorderLevel'].value = productData.reorderLevel || 0;
        form.elements['expiryDate'].value = productData.expiryDate || '';
        form.elements['dispatchStop'].value = productData.dispatchStop || 0;

        // Show modal
        $('#productModal').modal('show');
    });

    // Save button handler'ını güncelle
    $('#saveProduct').click(function () {
        const form = $('#productForm')[0];
        const productId = $('#productId').val();
        const itemNumber = form.elements['itemNumber'].value.trim();
        
        if (!itemNumber) {
            alert('Item Number is required');
            form.elements['itemNumber'].focus();
            return;
        }
        
        const productData = [{
            itemNumber: toStr(form.elements['itemNumber'].value),
            vatKind: toStr(form.elements['vatKind'].value),
            salesUnit: toInt(form.elements['salesUnit'].value),
            productGroup: toStr(form.elements['productGroup'].value),
            width: smartFloat(form.elements['width'].value, 1),
            height: smartFloat(form.elements['height'].value, 1),
            length: smartFloat(form.elements['length1'].value, 1),
            ean: toStr(form.elements['ean'].value),
            weight: smartFloat(form.elements['weight'].value, 1),
            country: toStr(form.elements['country'].value),
            description: toStr(form.elements['description'].value),
            tariffNumber: toStr(form.elements['tariffNumber'].value),
            lotNoRequired: toBool(form.elements['lotNoRequired'].value),
            purchasePrice: smartFloat(form.elements['purchasePrice'].value, 2),
            priceGross: smartFloat(form.elements['priceGross'].value, 2),
            priceNet: smartFloat(form.elements['priceNet'].value, 2),
            naliNili: toStr(form.elements['naliNili'].value),
            reorderLevel: toInt(form.elements['reorderLevel'].value),
            expiryDate: toStr(form.elements['expiryDate'].value),
            dispatchStop: toInt(form.elements['dispatchStop'].value),
            attributes: [
                {
                    description: "Subdepartment",
                    value: "Sales"
                }
            ]
        }];

        const rawJson = JSON.stringify(productData, null, 2);

        $.ajax({
            url: productId ? 
                '{{ route("product.update", {"id": "PLACEHOLDER"}) }}'.replace('PLACEHOLDER', productId) : 
                '{{ route("product.store") }}',
            method: 'POST',
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            data: rawJson,
            success: function (response) {
                if (response.success) {
                    $('#productModal').modal('hide');
                    window.location.reload();
                }
            },
            error: function (xhr) {
                alert('Error saving product: ' + (xhr.responseJSON?.message || 'Unknown error'));
            }
        });
    });

    // Edit product handler içine ekleyin
    $('#productModal .modal-title').text('Edit Product');

    // Modal kapandığında başlığı sıfırla
    $('#productModal').on('hidden.bs.modal', function () {
        $('#productId').val('');
        $('#productModal .modal-title').text('Product Details');
        $('#productForm')[0].reset();
    });

    // Ürün fotoğraflarını yükle
    function loadProductImages() {
        // Tablodaki her ürün satırı için
        $('tr[data-nurederm-id]').each(function() {
            const row = $(this);
            const itemCell = row.find('.product-item-cell');
            const nuredermId = row.data('nurederm-id');
            
            if (nuredermId) {
                // API'den ürün detaylarını çek
                $.ajax({
                    url: '/api/products-portal/' + nuredermId,
                    method: 'GET',
                    success: function(product) {
                        if (product && product.image_url) {
                            const itemNumber = itemCell.text().trim();
                            
                            // Ürün numarasının yanına resim ekle
                            itemCell.html(`
                                <div class="d-flex align-items-center">
                                    <span class="me-2">${itemNumber}</span>
                                    <a href="${product.image_url}" data-lightbox="product-image" data-title="${product.title || ''}">
                                        <img src="${product.image_url}" alt="${product.title || ''}" 
                                             class="rounded" style="width: 40px; height: 40px; object-fit: cover; cursor: pointer;">
                                    </a>
                                </div>
                            `);
                        }   
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching product details:', error);
                    }
                });
            }
        });
        
        // Lightbox'ı başlat
        lightbox.option({
            'resizeDuration': 200,
            'wrapAround': true
        });
    }

    // Stok verilerini çekme fonksiyonu
    function fetchStockData() {
        fetch('/api/stock-proxy', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(response => {
            if (response.data) {
                const stockMap = {};
                response.data.forEach(item => {
                    stockMap[item.productNumber] = item.stock;
                });

                document.querySelectorAll('.stock-cell').forEach(cell => {
                    const itemNumber = cell.dataset.itemNumber;
                    const stock = stockMap[itemNumber] !== undefined ? stockMap[itemNumber] : '-';
                    cell.innerHTML = stock;
                });
            }
        })
        .catch(error => {
            document.querySelectorAll('.stock-cell').forEach(cell => {
                cell.innerHTML = '-';
            });
            console.error('Error fetching stock data:', error);
        });
    }

    // Arşivleme işlemi
    $(document).on('click', '.archive-product', function() {
        if (confirm('Are you sure you want to archive this product?')) {
            const productId = $(this).data('id');
            const row = $(this).closest('tr');
            const itemNumber = row.find('.product-item-cell').text().trim();
            
            $.ajax({
                url: '{{ route("product.archive", {"id": "PLACEHOLDER"}) }}'.replace('PLACEHOLDER', productId),
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Bildirim göster
                        showNotification('Product ' + itemNumber + ' has been archived successfully.', 'success');
                        
                        // Sayfayı yenile veya satırı kaldır
                        row.fadeOut(300, function() {
                            $(this).remove();
                        });
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'Unknown error';
                    showNotification('Error archiving product: ' + errorMsg, 'danger');
                }
            });
        }
    });

    // Bildirim gösterme fonksiyonu
    function showNotification(message, type = 'info') {
        const id = 'notification-' + Date.now();
        const html = `
            <div id="${id}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        $('#notification-container').append(html);
        const toast = new bootstrap.Toast(document.getElementById(id), {
            delay: 5000
        });
        toast.show();
        
        // 5 saniye sonra otomatik kaldır
        setTimeout(() => {
            $(`#${id}`).remove();
        }, 5000);
    }

    // Sayfa yüklendiğinde çalıştır
    $(document).ready(function() {
        loadProductImages();
        fetchStockData();
    });

    // Refresh stock data every 5 minutes (300000 milliseconds)
    setInterval(fetchStockData, 300000);
});
</script>
{% endblock %}


