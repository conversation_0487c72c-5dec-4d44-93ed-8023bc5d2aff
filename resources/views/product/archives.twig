{% include 'html.twig' %}

{% block body %}

<style>
     @media (max-width: 600px) {

    .card-header .row {
        display: flex;
        flex-direction: column !important;
        justify-content: center;
    }

    .card-header .card-title {
        text-align: center;
    }

    .card-header .btn-primary {
        margin-top: 10px;
    }
}

</style>


{% include 'component/sidebar.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <!-- Notification area -->
            <div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
            
            <div class="row pt-4">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-body-tertiary">
                            <div class="row align-items-center">
                                <div class="col">                      
                                    <h4 class="card-title mb-0">Archived Products</h4>                      
                                </div>
                                <div class="col-auto">                      
                                    <a href="{{ url('/products') }}" class="btn btn-primary rounded-pill px-4">
                                        <i class="fas fa-box me-2"></i> Back to Products
                                    </a>
                                </div>
                            </div>                                  
                        </div>
                        <div class="card-body p-4">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>Item Number</th>
                                            <th>Description</th>
                                            <th>Price</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in archivedProducts %}
                                        {% set data = product.decoded_json[0] %}
                                        <tr>
                                            <td>{{ data.itemNumber }}</td>
                                            <td>{{ data.description }}</td>
                                            <td>€{{ data.priceGross }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-success restore-product" 
                                                        data-id="{{ product.id }}">
                                                    Restore
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSRF Token için meta tag -->
<meta name="csrf-token" content="{{ csrf_token() }}">

<script>
$(document).ready(function () {
    // CSRF Token ayarı
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Arşivden geri getirme işlemi
    $('.restore-product').click(function() {
        if (confirm('Are you sure you want to restore this product?')) {
            const productId = $(this).data('id');
            const row = $(this).closest('tr');
            const itemNumber = row.find('td:first').text().trim();
            
            $.ajax({
                url: '{{ route("product.restore", {"id": "PLACEHOLDER"}) }}'.replace('PLACEHOLDER', productId),
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        // Bildirim göster
                        showNotification('Product ' + itemNumber + ' has been restored successfully.', 'success');
                        
                        // Satırı kaldır
                        row.fadeOut(300, function() {
                            $(this).remove();
                        });
                        
                        // 2 saniye sonra ürünler sayfasına yönlendir
                        setTimeout(() => {
                            window.location.href = '{{ url("/products") }}';
                        }, 2000);
                    }
                },
                error: function(xhr) {
                    const errorMsg = xhr.responseJSON?.message || 'Unknown error';
                    showNotification('Error restoring product: ' + errorMsg, 'danger');
                }
            });
        }
    });
    
    // Bildirim gösterme fonksiyonu
    function showNotification(message, type = 'info') {
        const id = 'notification-' + Date.now();
        const html = `
            <div id="${id}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        $('#notification-container').append(html);
        const toast = new bootstrap.Toast(document.getElementById(id), {
            delay: 5000
        });
        toast.show();
        
        // 5 saniye sonra otomatik kaldır
        setTimeout(() => {
            $(`#${id}`).remove();
        }, 5000);
    }
});
</script>
{% endblock %}
