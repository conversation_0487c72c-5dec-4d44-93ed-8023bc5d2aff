
{# templates/sevdesk/show.html.twig #}

{% set sevdesk = invoice.sevdesk_response.objects %}
{% set invoiceData = sevdesk.invoice %}
{% set items = sevdesk.invoicePos %}
{% extends 'html.twig' %}

{% block body %}
{% include 'component/sidebar.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <!-- Enhanced Page Header -->
            <div class="row mb-4">
                <div class="col-sm-12">
                    <div class="page-title-box d-md-flex justify-content-md-between align-items-center bg-light rounded-3 p-4 shadow-sm">
                        <div>
                            <h3 class="page-title mb-1 fw-bold text-primary">Invoice #{{ invoiceData.invoiceNumber }}</h3>
                            <p class="text-muted mb-0 fs-6">Invoice details and line items</p>
                        </div>
                        <div>
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0 bg-transparent">
                                    <li class="breadcrumb-item"><a href="#" class="text-decoration-none">Portal</a></li>
                                    <li class="breadcrumb-item"><a href="{{ route('sevdesk.index') }}" class="text-decoration-none">SevDesk</a></li>
                                    <li class="breadcrumb-item active text-primary fw-semibold">Invoice</li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Row -->
            <div class="row g-4">
                <!-- Invoice Items Section -->
                <div class="col-lg-8">
                    <div class="card shadow-sm border-0 rounded-3 h-100">
                        <div class="card-header bg-gradient bg-primary bg-opacity-10 border-0 py-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="card-title mb-1 fw-bold text-dark">Invoice Items</h5>
                                    <p class="text-muted mb-0 fs-6">
                                        <i class="fas fa-list-ul me-1"></i>
                                        {{ items|length }} item{{ items|length != 1 ? 's' : '' }} in this invoice
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover table-striped mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="border-0 py-3 ps-4 fw-semibold text-muted">#</th>
                                            <th class="border-0 py-3 fw-semibold text-muted">Product Name</th>
                                            <th class="border-0 py-3 text-end fw-semibold text-muted">Qty</th>
                                            <th class="border-0 py-3 text-end fw-semibold text-muted">Net Price</th>
                                            <th class="border-0 py-3 text-end fw-semibold text-muted">Discount</th>
                                            <th class="border-0 py-3 text-end fw-semibold text-muted">Tax</th>
                                            <th class="border-0 py-3 text-end pe-4 fw-semibold text-muted">Gross</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in items %}
                                            {% set taxRate = item.taxRate ?? 19 %}
                                            {% set discountedNet = item.priceNet * (1 - (item.discount / 100)) %}
                                            {% set priceTax = discountedNet * (taxRate / 100) %}
                                            {% set priceGross = discountedNet + priceTax %}
                                            <tr class="border-bottom border-light">
                                                <td class="py-3 ps-4 text-muted fw-medium">{{ loop.index }}</td>
                                                <td class="py-3 fw-semibold">{{ item.name }}</td>
                                                <td class="py-3 text-end text-muted">{{ item.quantity }}</td>
                                                <td class="py-3 text-end fw-medium">€{{ item.priceNet|number_format(2, ',', '.') }}</td>
                                                <td class="py-3 text-end text-warning fw-medium">%{{ item.discount|number_format(2, ',', '.') }}</td>
                                                <td class="py-3 text-end text-warning fw-medium">€{{ priceTax|number_format(2, ',', '.') }}</td>
                                                <td class="py-3 text-end pe-4 fw-bold text-success">€{{ priceGross|number_format(2, ',', '.') }}</td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Section -->
                <div class="col-lg-4">
                    <!-- Customer Information Card -->
                    <div class="card shadow-sm border-0 rounded-3 mb-4">
                        <div class="card-header bg-gradient bg-info bg-opacity-10 border-0 py-3">
                            <h5 class="card-title mb-0 fw-bold text-dark">
                                <i class="fas fa-user me-2 text-info"></i>Customer Information
                            </h5>
                        </div>
                        <div class="card-body py-4">
                            <div class="d-flex align-items-center mb-3">
                                <div class="bg-info bg-opacity-10 rounded-circle p-2 me-3">
                                    <i class="fas fa-user text-info"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0 fw-bold text-dark">{{ invoice.customer.first_name }} {{ invoice.customer.last_name }}</h6>
                                    {% if invoice.customer.company %}
                                        <small class="text-muted">{{ invoice.customer.company }}</small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="border-start border-info border-3 ps-3">
                                <p class="mb-0 text-muted lh-lg">
                                    <i class="fas fa-map-marker-alt me-2 text-info"></i>
                                    {{ invoice.customer.address }} {{ invoice.customer.house_number }}<br>
                                    {{ invoice.customer.zip_code }} {{ invoice.customer.city }}<br>
                                    <strong>{{ invoice.customer.country }}</strong>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Summary Card -->
                    <div class="card shadow-sm border-0 rounded-3 mb-4">
                        <div class="card-header bg-gradient bg-success bg-opacity-10 border-0 py-3">
                            <h5 class="card-title mb-0 fw-bold text-dark">
                                <i class="fas fa-calculator me-2 text-success"></i>Financial Summary
                            </h5>
                        </div>
                        <div class="card-body py-4">
                            <div class="row g-3">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center py-2">
                                        <span class="text-muted fw-medium">
                                            <i class="fas fa-minus-circle me-2 text-muted"></i>Subtotal (Net):
                                        </span>
                                        <span class="fw-bold fs-6">€{{ invoiceData.sumNet|number_format(2, ',', '.') }}</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex justify-content-between align-items-center py-2">
                                        <span class="text-muted fw-medium">
                                            <i class="fas fa-percentage me-2 text-warning"></i>Tax:
                                        </span>
                                        <span class="fw-bold fs-6 text-warning">€{{ invoiceData.sumTax|number_format(2, ',', '.') }}</span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <hr class="my-2">
                                    <div class="d-flex justify-content-between align-items-center py-2 bg-success bg-opacity-10 rounded-2 px-3">
                                        <span class="fw-bold text-dark">
                                            <i class="fas fa-euro-sign me-2 text-success"></i>Total (Gross):
                                        </span>
                                        <span class="fw-bold fs-5 text-success">€{{ invoiceData.sumGross|number_format(2, ',', '.') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Terms Card -->
                    <div class="card shadow-sm border-0 rounded-3">
                        <div class="card-header bg-gradient bg-warning bg-opacity-10 border-0 py-3">
                            <h5 class="card-title mb-0 fw-bold text-dark">
                                <i class="fas fa-credit-card me-2 text-warning"></i>Payment Terms
                            </h5>
                        </div>
                        <div class="card-body py-4">
                            <div class="row g-3">
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-3 bg-light rounded-2">
                                        <div class="bg-warning bg-opacity-20 rounded-circle p-2 me-3">
                                            <i class="fas fa-clock text-warning"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">Time to Pay</small>
                                            <span class="fw-bold text-dark">{{ invoiceData.timeToPay }} days</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-3 bg-light rounded-2">
                                        <div class="bg-primary bg-opacity-20 rounded-circle p-2 me-3">
                                            <i class="fas fa-paper-plane text-primary"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">Send Type</small>
                                            <span class="fw-bold text-dark">{{ invoiceData.sendType }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="d-flex align-items-center p-3 bg-light rounded-2">
                                        <div class="bg-success bg-opacity-20 rounded-circle p-2 me-3">
                                            <i class="fas fa-coins text-success"></i>
                                        </div>
                                        <div>
                                            <small class="text-muted d-block">Currency</small>
                                            <span class="fw-bold text-dark">{{ invoiceData.currency }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
