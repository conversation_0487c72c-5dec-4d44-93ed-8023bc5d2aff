{% extends 'html.twig' %}

{% block body %}


<style>

    @media(max-width: 576px) {
        .col-sm-12 .page-title-box {
            flex-direction: column-reverse !important;
            row-gap: 1.5rem !important;
        }
    }

    .select2-selection__arrow {
        right: 5px !important;
        top: 5px !important;
    }

    .select2-container {
        width: 100% !important;
    }

    /* Compact item layout styling - Light Mode */
    .invoice-item {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6 !important;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .invoice-item:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
    }

    .invoice-item .form-label.small {
        font-size: 0.75rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: #495057;
        transition: color 0.3s ease;
    }

    .invoice-item .form-control-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border: 1px solid #ced4da;
        transition: all 0.3s ease;
    }

    .invoice-item .form-control-sm:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .invoice-item .remove-item {
        font-size: 1.2rem;
        font-weight: bold;
        padding: 0.25rem 0.5rem;
        transition: all 0.3s ease;
    }

    .invoice-item .remove-item:hover {
        background-color: #dc3545;
        border-color: #dc3545;
        transform: scale(1.05);
    }

    /* Dark Mode Styling */
    [data-bs-theme="dark"] .invoice-item {
        background-color: #2b2b2b;
        border: 1px solid #404040 !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    [data-bs-theme="dark"] .invoice-item:hover {
        background-color: #333333;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
        border-color: #555555 !important;
    }

    [data-bs-theme="dark"] .invoice-item .form-label.small {
        color: #e9ecef;
    }

    [data-bs-theme="dark"] .invoice-item .form-control-sm {
        background-color: #1a1a1a;
        border-color: #404040;
        color: #e9ecef;
    }

    [data-bs-theme="dark"] .invoice-item .form-control-sm:focus {
        background-color: #1a1a1a;
        border-color: #0d6efd;
        color: #e9ecef;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    [data-bs-theme="dark"] .invoice-item .form-control-sm::placeholder {
        color: #6c757d;
    }

    [data-bs-theme="dark"] .invoice-item .remove-item {
        background-color: #dc3545;
        border-color: #dc3545;
        color: #fff;
    }

    [data-bs-theme="dark"] .invoice-item .remove-item:hover {
        background-color: #bb2d3b;
        border-color: #b02a37;
    }

    /* Autocomplete dropdown dark mode */
    [data-bs-theme="dark"] .autocomplete-dropdown {
        background-color: #2b2b2b !important;
        border-color: #404040 !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
    }


    [data-bs-theme="dark"] .autocomplete-item {
        color: #e9ecef !important;
        border-bottom-color: #404040 !important;
    }

    [data-bs-theme="dark"] .autocomplete-item:hover {
        background-color: #404040 !important;
        color: #fff !important;
    }

    /* Add Item Button Dark Mode */
    [data-bs-theme="dark"] #addItemBtn {
        background-color: #198754;
        border-color: #198754;
    }

    [data-bs-theme="dark"] #addItemBtn:hover {
        background-color: #157347;
        border-color: #146c43;
        box-shadow: 0 2px 8px rgba(25, 135, 84, 0.3);
    }

    /* Invoice Items Header Dark Mode */
    [data-bs-theme="dark"] h3 {
        color: #e9ecef;
    }

    /* Form Controls Dark Mode Enhancement */
    [data-bs-theme="dark"] .form-control {
        background-color: #1a1a1a;
        border-color: #404040;
        color: #e9ecef;
    }

    [data-bs-theme="dark"] .form-control:focus {
        background-color: #1a1a1a;
        border-color: #0d6efd;
        color: #e9ecef;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    [data-bs-theme="dark"] .form-control::placeholder {
        color: #6c757d;
    }

    [data-bs-theme="dark"] .form-label {
        color: #e9ecef;
    }

    [data-bs-theme="dark"] .input-group-text {
        background-color: #2b2b2b;
        border-color: #404040;
        color: #e9ecef;
    }

    /* Card Dark Mode */
    [data-bs-theme="dark"] .card {
        background-color: #1a1a1a;
        border-color: #404040;
    }

    [data-bs-theme="dark"] .card-body {
        background-color: #1a1a1a;
    }

    /* Button Dark Mode Enhancements */
    [data-bs-theme="dark"] .btn-primary {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    [data-bs-theme="dark"] .btn-primary:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
        box-shadow: 0 2px 8px rgba(13, 110, 253, 0.3);
    }

    [data-bs-theme="dark"] .btn-info {
        background-color: #0dcaf0;
        border-color: #0dcaf0;
        color: #000;
    }

    [data-bs-theme="dark"] .btn-info:hover {
        background-color: #31d2f2;
        border-color: #25cff2;
        box-shadow: 0 2px 8px rgba(13, 202, 240, 0.3);
    }

    [data-bs-theme="dark"] .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    [data-bs-theme="dark"] .btn-secondary:hover {
        background-color: #5c636a;
        border-color: #565e64;
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
    }

    /* Page Title Dark Mode */
    [data-bs-theme="dark"] .page-title {
        color: #e9ecef;
    }

    /* Textarea Dark Mode */
    [data-bs-theme="dark"] textarea.form-control {
        background-color: #1a1a1a;
        border-color: #404040;
        color: #e9ecef;
    }

    [data-bs-theme="dark"] textarea.form-control:focus {
        background-color: #1a1a1a;
        border-color: #0d6efd;
        color: #e9ecef;
    }

    /* Page Layout Dark Mode */
    [data-bs-theme="dark"] .page-wrapper {
        background-color: #121212;
    }

    [data-bs-theme="dark"] .page-content {
        background-color: #121212;
    }

    [data-bs-theme="dark"] .container-fluid {
        background-color: #121212;
    }

    /* Page Title Box Dark Mode */
    [data-bs-theme="dark"] .page-title-box {
        background-color: transparent;
    }

    [data-bs-theme="dark"] .page-title-box h4 {
        color: #e9ecef;
    }

    /* Enhanced transitions for smooth theme switching */
    .invoice-item,
    .form-control,
    .btn,
    .card,
    .autocomplete-dropdown,
    .autocomplete-item {
        transition: all 0.3s ease;
    }

    /* Focus ring improvements for dark mode */
    [data-bs-theme="dark"] .form-control:focus,
    [data-bs-theme="dark"] .invoice-item .form-control-sm:focus {
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        border-color: #0d6efd;
    }

    /* Improved contrast for better accessibility */
    [data-bs-theme="dark"] .invoice-item .form-label.small {
        font-weight: 700;
        color: #f8f9fa;
    }

    /* Loading states for dark mode */
    [data-bs-theme="dark"] .spinner-border {
        color: #0d6efd;
    }

    /* Stock validation styling */
    .item-quantity.is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        animation: shake 0.5s ease-in-out;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    /* Dark mode invalid styling */
    [data-bs-theme="dark"] .item-quantity.is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }

    /* Invoice item form alignment improvements */
    .invoice-item .row {
        align-items: end !important;
    }

    .invoice-item .form-label.small {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-height: 1.2rem;
        display: block;
    }

    .invoice-item .form-control-sm {
        min-height: calc(1.5em + 0.5rem + 2px);
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 768px) {
        .invoice-item .col-md-4,
        .invoice-item .col-md-2,
        .invoice-item .col-md-1 {
            margin-bottom: 0.5rem;
        }

        .invoice-item .form-label.small {
            font-size: 0.7rem;
        }
    }

    /* Ensure consistent button alignment */
    .invoice-item .remove-item {
        margin-top: 0;
        height: calc(1.5em + 0.5rem + 2px);
        display: flex;
        align-items: center;
        justify-content: center;
    }


</style>


{% include 'component/sidebar.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12">
                    <div class="page-title-box d-flex justify-content-between align-items-center">
                        <h4 class="page-title">Create Sevdesk Invoice</h4>
                        <div>
                            <a href="{{ route('sevdesk.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <form id="createSevdeskInvoiceForm">
                        <div class="row">
                            <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="invoiceNumber" class="form-label">Invoice Number</label>
                                <div class="input-group">
                                    <span class="input-group-text">NRD-</span>
                                    <input type="number" min="1000" class="form-control" id="invoiceNumberSuffix" name="invoice[invoiceNumberSuffix]"
                                        value="{{ invoice.invoiceNumberSuffix is defined 
                                                    ? invoice.invoiceNumberSuffix 
                                                    : (nextInvoiceNumber|replace({'NRD-': ''})) }}"
                                        required>
                                </div>
                            </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="invoiceDate" class="form-label">Invoice Date</label>
                                    <input type="date" class="form-control" id="invoiceDate" name="invoice[invoiceDate]" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Contact</label>
                                    <select class="form-control" id="contact" name="invoice[contact]">
                                        <option value="">Select Contact</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="contactPerson" class="form-label">Contact Person</label>
                                    <select class="form-control" id="contactPerson" name="invoice[contactPerson]">
                                        <option value="">Select Contact Person</option>
                                        {% for user in sevUsers %}
                                            <option value="{{ user.id }}">{{ user.firstName }} {{ user.lastName }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Street Name and Number</label>
                                    <input type="text" class="form-control" id="streetAddress" name="invoice[streetAddress]">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Zip Code</label>
                                    <input type="text" class="form-control" id="zipCode" name="invoice[zipCode]">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">City</label>
                                    <input type="text" class="form-control" id="city" name="invoice[city]">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Country</label>
                                    <input type="text" class="form-control" id="country" name="invoice[country]">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                        <!-- <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="header" class="form-label">Header</label>
                                    <input type="text" class="form-control" id="header" name="invoice[header]" value="Rechnung Nr. RE-{{ nextInvoiceNumber|replace({'NRD-': ''}) }}">
                                </div>
                            </div> -->
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="timeToPay" class="form-label">Time to Pay (days)</label>
                                    <input type="number" class="form-control" id="timeToPay" name="invoice[timeToPay]" value="20">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="headText" class="form-label">Header Text</label>
                                    <textarea class="form-control" id="headText" name="invoice[headText]" rows="2">vielen Dank für Ihren Auftrag und das damit verbundene Vertrauen.
Folgende Produkte werden Ihnen zur Rechnung gestellt:</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="footText" class="form-label">Footer Text</label>
                                  <textarea class="form-control" id="footText" name="invoice[footText]" rows="12">Bitte überweisen Sie den Rechnungsbetrag unter Angabe der Rechnungsnummer auf das unten angegebene Konto.<br><br>Der Rechnungsbetrag ist sofort fällig.<br><br>Empfänger: Nurederm GmbH<br>Bankname: FINOM PAYMENTS<br>IBAN: DE40 1001 8000 0444 5077 46<br>BIC: FNOMDEB2<br><br><br>Mit freundlichen Grüßen</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="taxType" class="form-label">Tax Type</label>
                                    <select class="form-control" id="taxType" name="invoice[taxType]">
                                        <option value="default">Default</option>
                                        <option value="eu">EU</option>
                                        <option value="noteu">Non-EU</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="invoiceTaxRate" class="form-label">Default Tax Rate</label>
                                    <select class="form-control" id="invoiceTaxRate" name="invoice[taxRate]">
                                        <option value="19" selected>19%</option>
                                        <option value="7">7%</option>
                                        <option value="0">0%</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="globalDiscount" class="form-label">Global Discount (%)</label>
                                    <input type="number" class="form-control" id="globalDiscount" name="invoice[globalDiscount]" value="0" min="0" max="100" step="0.01">
                                </div>
                            </div>
                        </div>

                        <h3 class="mt-4">Invoice Items</h3>
                        <div id="invoiceItems">
                            <div class="invoice-item border rounded p-3 mb-2">
                                <div class="row align-items-end g-2">
                                    <!-- Hidden item number field for backend processing -->
                                    <input type="hidden" class="item-number-hidden" name="items[0][item_number]">

                                    <div class="col-md-4">
                                        <label class="form-label small">Product Name</label>
                                        <input type="text" class="form-control form-control-sm" name="items[0][product_name]" placeholder="Search for product..." autocomplete="off" required>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label small">Qty</label>
                                        <input type="number" class="form-control form-control-sm item-quantity" value="1" min="1" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">Price Net</label>
                                        <input type="number" class="form-control form-control-sm item-price" name="items[0][price]" step="0.01" required>
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label small">Disc %</label>
                                        <input type="number" class="form-control form-control-sm item-discount" name="items[0][discount]" value="0" min="0" max="100" step="0.01">
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label small">Tax %</label>
                                        <select class="form-control form-control-sm item-tax-rate">
                                            <option value="19">19%</option>
                                            <option value="7">7%</option>
                                            <option value="0">0%</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label small">Price Gross</label>
                                        <input type="number" class="form-control form-control-sm item-price-gross" name="items[0][price_gross]" step="0.01">
                                    </div>
                                    <div class="col-md-1">
                                        <label class="form-label small d-block">&nbsp;</label>
                                        <button type="button" class="btn btn-danger btn-sm remove-item w-100">×</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-start mb-3">
                            <button type="button" id="addItemBtn" class="btn btn-success">
                                Add Item
                            </button>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <div class="d-flex align-items-center me-3">
                                <span class="me-2">Total Price:</span>
                                <span id="totalPrice" class="h4 mb-0">€0.00</span>
                            </div>
                            <button type="button" id="saveAsTemplateBtn" class="btn btn-info">
                                Save as Draft
                            </button>
                            <button type="button" id="submitInvoiceBtn" class="btn btn-primary">
                                Create Invoice
                            </button>
                        </div>
                        
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<!-- Include Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Simple Select2 Styling -->
<style>
/* Basic Select2 styling to match form controls - Light Mode */
.select2-container--default .select2-selection--single {
    height: 38px !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.375rem !important;
    font-size: 0.875rem !important;
    background-color: #fff !important;
    transition: all 0.3s ease;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #212529 !important;
    padding-left: 12px !important;
    line-height: 36px !important;
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #6c757d !important;
}

.select2-dropdown {
    border: 1px solid #ced4da !important;
    border-radius: 0.375rem !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.select2-container--default .select2-results__option {
    padding: 8px 12px !important;
    font-size: 0.875rem !important;
    transition: all 0.2s ease;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0d6efd !important;
    color: #fff !important;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #ced4da !important;
    border-radius: 0.375rem !important;
    padding: 6px 12px !important;
    font-size: 0.875rem !important;
}

/* Select2 Dark Mode Styling */
[data-bs-theme="dark"] .select2-container--default .select2-selection--single {
    background-color: #1a1a1a !important;
    border-color: #404040 !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #6c757d !important;
}

[data-bs-theme="dark"] .select2-dropdown {
    background-color: #2b2b2b !important;
    border-color: #404040 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

[data-bs-theme="dark"] .select2-container--default .select2-results__option {
    background-color: #2b2b2b !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .select2-container--default .select2-results__option:hover {
    background-color: #404040 !important;
}

[data-bs-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0d6efd !important;
    color: #fff !important;
}

[data-bs-theme="dark"] .select2-container--default .select2-search--dropdown .select2-search__field {
    background-color: #1a1a1a !important;
    border-color: #404040 !important;
    color: #e9ecef !important;
}

[data-bs-theme="dark"] .select2-container--default .select2-search--dropdown .select2-search__field::placeholder {
    color: #6c757d !important;
}

/* Select2 Focus States */
[data-bs-theme="dark"] .select2-container--default.select2-container--focus .select2-selection--single {
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}
</style>

<script>
 function populateFormFromTemplate(templateData, selectedCustomer = null) {
        if (!templateData || !templateData.invoice) return;

        const invoice = templateData.invoice;

        // Populate basic invoice fields
        if (invoice.timeToPay) $('#timeToPay').val(invoice.timeToPay);
        if (invoice.headText) $('#headText').val(invoice.headText);
        if (invoice.footText) $('#footText').val(invoice.footText);
        if (invoice.taxType) $('#taxType').val(invoice.taxType);

        // Populate contact person if available
        if (invoice.contactPerson && invoice.contactPerson.id) {
            $('#contactPerson').val(invoice.contactPerson.id);
        }

        // Auto-select customer if available
        if (selectedCustomer) {
            // Create option element for Select2
            const customerText = selectedCustomer.client_id + ' - ' + selectedCustomer.first_name + ' ' + selectedCustomer.last_name +
                                (selectedCustomer.company ? ' (' + selectedCustomer.company + ')' : '');

            const option = new Option(customerText, selectedCustomer.client_id, true, true);
            $('#contact').append(option).trigger('change');

            // Populate address fields from customer data
            const address = selectedCustomer.address || '';
            const houseNumber = selectedCustomer.house_number || '';
            const zipCode = selectedCustomer.zip_code || '';
            const city = selectedCustomer.city || '';
            const country = selectedCustomer.country || '';

            // Combine address and house number for street address field
            const streetAddress = address && houseNumber ? `${address} ${houseNumber}` : (address || houseNumber || '');

            $('#streetAddress').val(streetAddress);
            $('#zipCode').val(zipCode);
            $('#city').val(city);
            $('#country').val(country);
        } else if (invoice.address) {
            // Fallback: populate address fields from template if no customer found
            const addressLines = invoice.address.split('\n');
            if (addressLines.length > 0) $('#streetAddress').val(addressLines[0]);
            if (addressLines.length > 1) {
                const zipCityLine = addressLines[1];
                const zipCityMatch = zipCityLine.match(/^(\d+)\s+(.+)$/);
                if (zipCityMatch) {
                    $('#zipCode').val(zipCityMatch[1]);
                    $('#city').val(zipCityMatch[2]);
                }
            }
            if (addressLines.length > 2) $('#country').val(addressLines[2]);
        }

        // Clear existing items except the first one
        $('.invoice-item').not(':first').remove();

        // Populate invoice items if available
        if (templateData.invoicePosSave && templateData.invoicePosSave.length > 0) {
            templateData.invoicePosSave.forEach(function(item, index) {
                let itemContainer;

                if (index === 0) {
                    // Use the first existing item
                    itemContainer = $('.invoice-item').first();
                } else {
                    // Clone the first item for additional items
                    itemContainer = $('.invoice-item').first().clone();

                    // Update name attributes for the new item
                    itemContainer.find('.item-number-hidden').attr('name', `items[${index}][item_number]`);
                    itemContainer.find('input[name$="[product_name]"]').attr('name', `items[${index}][product_name]`);
                    itemContainer.find('input[name$="[price]"]').attr('name', `items[${index}][price]`);
                    itemContainer.find('input[name$="[discount]"]').attr('name', `items[${index}][discount]`);
                    itemContainer.find('input[name$="[price_gross]"]').attr('name', `items[${index}][price_gross]`);

                    $('#invoiceItems').append(itemContainer);
                }

                // Populate item fields
                if (item.itemNumber !== undefined) itemContainer.find('.item-number-hidden').val(item.itemNumber);
                if (item.name !== undefined) itemContainer.find('input[name$="[product_name]"]').val(item.name);
                if (item.quantity !== undefined) itemContainer.find('.item-quantity').val(item.quantity);
                if (item.price !== undefined) itemContainer.find('.item-price').val(item.price);
                if (item.discount !== undefined) itemContainer.find('.item-discount').val(item.discount);
                if (item.taxRate !== undefined) itemContainer.find('.item-tax-rate').val(item.taxRate);
                if (item.priceGross !== undefined) itemContainer.find('.item-price-gross').val(item.priceGross);

            });
        }
    }
$(document).ready(function() {
    const today = new Date().toISOString().split('T')[0];
    $('#invoiceDate').val(today);

    // Total price hesaplama (discount dahil)
    function calculateTotal() {
        let total = 0;
        $('.invoice-item').each(function() {
            const price = parseFloat($(this).find('.item-price').val()) || 0;
            const quantity = parseFloat($(this).find('.item-quantity').val()) || 1;
            const discount = parseFloat($(this).find('.item-discount').val()) || 0;
            const taxRate = parseFloat($(this).find('.item-tax-rate').val()) || 19; // Default tax rate
            
            // İndirim tutarını hesapla ve fiyattan düş
            const discountAmount = price * (discount / 100);
            const finalPrice = price - discountAmount;
            
            total += (finalPrice * quantity) * (1 + (taxRate / 100));
        });
        $('#totalPrice').text('€' + total.toFixed(2));
    }

    // Price veya discount değiştiğinde hesapla
    $(document).on('input', '.item-price, .item-discount, .item-quantity, .item-tax-rate', calculateTotal);

    // Ürün seçildiğinde hesapla
    const originalVal = $.fn.val;
    $.fn.val = function(value) {
        const result = originalVal.apply(this, arguments);
        if ($(this).hasClass('item-price') && arguments.length > 0) {
            setTimeout(calculateTotal, 50);
        }
        return result;
    };

    // Global discount handler
    $('#globalDiscount').on('input', function() {
        const globalDiscount = parseFloat($(this).val()) || 0;
        $('.invoice-item').each(function() {
            $(this).find('.item-discount').val(globalDiscount);
        });
    });

    $('#contact').select2({
        ajax: {
            url: '{{ route("sevdesk.search") }}',
            type: 'GET',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    term: params.term || ''
                };
            },
            processResults: function(data) {
                return {
                    results: data.map(function(item) {
                        return {
                            id: item.client_id,
                            text: item.client_id + ' - ' + item.first_name + ' ' + item.last_name + (item.company ? ' (' + item.company + ')' : ''),
                            first_name: item.first_name,
                            last_name: item.last_name,
                            address: item.address,
                            house_number: item.house_number,
                            zip_code: item.zip_code,
                            company: item.company,
                            city: item.city,
                            country: item.country
                        };
                    })
                };
            },
            cache: true
        },
        minimumInputLength: 2,
        placeholder: 'Search for a customer...',
        allowClear: true
    });

    // Check if template data is available and populate form after Select2 initialization
    {% if templateData is defined %}
    const templateData = {{ templateData|json_encode|raw }};
    {% if selectedCustomer is defined %}
    const selectedCustomer = {{ selectedCustomer|json_encode|raw }};
    {% else %}
    const selectedCustomer = null;
    {% endif %}
    // Use setTimeout to ensure Select2 is fully initialized
    setTimeout(function() {
        populateFormFromTemplate(templateData, selectedCustomer);
    }, 100);
    {% endif %}

    // Add new item
    $('#addItemBtn').click(function() {
        const newItem = $('.invoice-item').first().clone();
        const itemIndex = $('.invoice-item').length;
        const globalDiscount = parseFloat($('#globalDiscount').val()) || 0;

        newItem.find('input').val('');
        newItem.find('input.item-quantity').val(1);
        newItem.find('input.item-discount').val(globalDiscount);

        // Reset quantity constraints for new item
        newItem.find('.item-quantity').removeAttr('max');
        newItem.find('label:contains("Quantity")').html('Quantity');

        // Update name attributes for the new item
        newItem.find('.item-number-hidden').attr('name', `items[${itemIndex}][item_number]`);
        newItem.find('input[name$="[product_name]"]').attr('name', `items[${itemIndex}][product_name]`);
        newItem.find('input[name$="[price]"]').attr('name', `items[${itemIndex}][price]`);
        newItem.find('input[name$="[discount]"]').attr('name', `items[${itemIndex}][discount]`);
        newItem.find('input[name$="[price_gross]"]').attr('name', `items[${itemIndex}][price_gross]`);

        $('#invoiceItems').append(newItem);
    });

    // Remove item
    $(document).on('click', '.remove-item', function() {
        if ($('.invoice-item').length > 1) {
            $(this).closest('.invoice-item').remove();
            calculateTotal(); // Ürün silindiğinde total price'ı güncelle
        } else {
            alert('At least one item is required');
        }
    });

    $('#contact').on('select2:select', function(e) {
        const selectedData = e.params.data;

        if (selectedData.id) {
            const address = selectedData.address || '';
            const houseNumber = selectedData.house_number || '';
            const zipCode = selectedData.zip_code || '';
            const city = selectedData.city || '';
            const country = selectedData.country || '';

            currentContactData = e.params.data;

            // Combine address and house number for street address field
            const streetAddress = address && houseNumber ? `${address} ${houseNumber}` : (address || houseNumber || '');

            $('#streetAddress').val(streetAddress);
            $('#zipCode').val(zipCode);
            $('#city').val(city);
            $('#country').val(country);
        }
    });

    // Clear fields when contact is cleared
    $('#contact').on('select2:clear', function() {
        $('#streetAddress').val('');
        $('#zipCode').val('');
        $('#city').val('');
        $('#country').val('');
    });

    $('#saveAsTemplateBtn').click(function() {
        if (!$('#createSevdeskInvoiceForm')[0].checkValidity()) {
            $('#createSevdeskInvoiceForm')[0].reportValidity();
            return;
        }

        // Format date as DD.MM.YYYY
        const invoiceDate = $('#invoiceDate').val();
        const dateParts = invoiceDate.split('-');
        const formattedDate = dateParts[2] + '.' + dateParts[1] + '.' + dateParts[0];

        const selectedContactId = $('#contact').val() || 104806333; 
        const selectedContactPersonId = $('#contactPerson').val() || 968567; 

        // Combine address fields into German format for SevDesk API
        const streetAddress = $('#streetAddress').val().trim();
        const zipCode = $('#zipCode').val().trim();
        const city = $('#city').val().trim();
        const country = $('#country').val().trim();

        let combinedAddress = '';

        if (streetAddress || zipCode || city || country) {
            const addressParts = [];

            if (streetAddress) {
                addressParts.push(streetAddress);
            }

            if (zipCode && city) {
                addressParts.push(zipCode + ' ' + city);
            } else if (zipCode) {
                addressParts.push(zipCode);
            } else if (city) {
                addressParts.push(city);
            }

            if (country) {
                addressParts.push(country);
            }

            combinedAddress = addressParts.join('\n');
        }

        const invoiceData = {
            invoice: {
                objectName: "Invoice",
                invoiceNumber: $('#invoiceNumber').val(),
                invoiceDate: formattedDate,
                header: $('#header').val(),
                headText: $('#headText').val(),
                footText: $('#footText').val(),
                timeToPay: $('#timeToPay').val(),
                discount: 0,
                address: combinedAddress,
                addressCountry: $('#country').val(),
                status: "100", // 100 = Draft
                taxType: $('#taxType').val(),
                taxRate: 19, // Default tax rate for the invoice
                taxText: "Umsatzsteuer 19%", // Default tax text
                invoiceType: "RE",
                currency: "EUR",
                showNet: "1",
                sendType: "VPR",
                mapAll: true,
                contact: {
                    id: parseInt(selectedContactId),
                    objectName: "Contact"
                },
                contactPerson: {
                    id: parseInt(selectedContactPersonId),
                    objectName: "SevUser"
                }
            },
            invoicePosSave: [],
            invoicePosDelete: null,
            discountSave: [],
            discountDelete: null
        };

        // Add invoice items
        $('.invoice-item').each(function(index) {
            const item = $(this);
            const itemNumberField = item.find('.item-number-hidden');
            const quantity = item.find('.item-quantity').val();
            const price = item.find('.item-price').val();
            const taxRate = item.find('.item-tax-rate').val() || "19";
            const discount = item.find('.item-discount').val() || 0;

            if (itemNumberField.val() && quantity && price && item.find('input[name$="[product_name]"]').val()) {
                invoiceData.invoicePosSave.push({
                    objectName: "InvoicePos",
                    mapAll: true,
                    quantity: parseInt(quantity),
                    price: parseFloat(price),
                    itemNumber: itemNumberField.val(),
                    positionNumber: index + 1,
                    name: item.find('input[name$="[product_name]"]').val(),
                    text: item.find('input[name$="[product_name]"]').val(),
                    discount: parseFloat(discount),
                    taxRate: parseInt(taxRate),
                    unity: {
                        id: 1,
                        objectName: "Unity"
                    },
                    priceGross: parseFloat(price) * (1 + (parseInt(taxRate) / 100)),
                    priceTax: parseFloat(price) * (parseInt(taxRate) / 100)
                });
            }
        });

        // Send to server
        $.ajax({
            url: '{{ route("sevdesk.store") }}?isTemplate=true&suffix=' + $('#invoiceNumberSuffix').val(),
            method: 'POST',
            data: JSON.stringify(invoiceData),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert('Invoice created successfully!');
                    window.location.href = '{{ route("sevdesk.index") }}';
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                let msg = xhr.responseJSON?.message || 'Unknown error';

                if (xhr.responseJSON?.error) {
                    const stockErrors = xhr.responseJSON.error;

                    if (Array.isArray(stockErrors)) {
                        const issues = stockErrors.map(issue =>
                            `• ${issue.issue}`
                        ).join('\n');
                        msg += '\n\nDetails:\n' + issues;
                    }
                }

                alert('Error creating invoice:\n\n' + msg);
            }
        });
    });
    // Submit form
    $('#submitInvoiceBtn').click(function() {
        if (!$('#createSevdeskInvoiceForm')[0].checkValidity()) {
            $('#createSevdeskInvoiceForm')[0].reportValidity();
            return;
        }

        // Format date as DD.MM.YYYY
        const invoiceDate = $('#invoiceDate').val();
        const dateParts = invoiceDate.split('-');
        const formattedDate = dateParts[2] + '.' + dateParts[1] + '.' + dateParts[0];

        const selectedContactId = $('#contact').val() || 104806333; 
        const selectedContactPersonId = $('#contactPerson').val() || 968567; 

        // Combine address fields into German format for SevDesk API
        const streetAddress = $('#streetAddress').val().trim();
        const zipCode = $('#zipCode').val().trim();
        const city = $('#city').val().trim();
        const country = $('#country').val().trim();

        let combinedAddress = '';

        if (streetAddress || zipCode || city || country) {
            const addressParts = [];

            if (streetAddress) {
                addressParts.push(streetAddress);
            }

            if (zipCode && city) {
                addressParts.push(zipCode + ' ' + city);
            } else if (zipCode) {
                addressParts.push(zipCode);
            } else if (city) {
                addressParts.push(city);
            }

            if (country) {
                addressParts.push(country);
            }

            combinedAddress = addressParts.join('\n');
        }

        const invoiceData = {
            invoice: {
                objectName: "Invoice",
                invoiceNumber: $('#invoiceNumber').val(),
                invoiceDate: formattedDate,
                header: $('#header').val(),
                headText: $('#headText').val(),
                footText: $('#footText').val(),
                timeToPay: $('#timeToPay').val(),
                discount: 0,
                address: combinedAddress,
                addressCountry: $('#country').val(),
                status: "100", // 100 = Draft
                taxType: $('#taxType').val(),
                taxRate: 19, // Default tax rate for the invoice
                taxText: "Umsatzsteuer 19%", // Default tax text
                invoiceType: "RE",
                currency: "EUR",
                showNet: "1",
                sendType: "VPR",
                mapAll: true,
                contact: {
                    id: parseInt(selectedContactId),
                    objectName: "Contact"
                },
                contactPerson: {
                    id: parseInt(selectedContactPersonId),
                    objectName: "SevUser"
                }
            },
            invoicePosSave: [],
            invoicePosDelete: null,
            discountSave: [],
            discountDelete: null
        };

        // Add invoice items
        $('.invoice-item').each(function(index) {
            const item = $(this);
            const itemNumberField = item.find('.item-number-hidden');
            const quantity = item.find('.item-quantity').val();
            const price = item.find('.item-price').val();
            const taxRate = item.find('.item-tax-rate').val() || "19";
            const discount = item.find('.item-discount').val() || 0;

            if (itemNumberField.val() && quantity && price && item.find('input[name$="[product_name]"]').val()) {
                invoiceData.invoicePosSave.push({
                    objectName: "InvoicePos",
                    mapAll: true,
                    quantity: parseInt(quantity),
                    price: parseFloat(price),
                    itemNumber: itemNumberField.val(),
                    positionNumber: index + 1,
                    name: item.find('input[name$="[product_name]"]').val(),
                    text: item.find('input[name$="[product_name]"]').val(),
                    discount: parseFloat(discount),
                    taxRate: parseInt(taxRate),
                    unity: {
                        id: 1,
                        objectName: "Unity"
                    },
                    priceGross: parseFloat(price) * (1 + (parseInt(taxRate) / 100)),
                    priceTax: parseFloat(price) * (parseInt(taxRate) / 100)
                });
            }
        });
        
        $.ajax({
            url: '{{ route("sevdesk.store") }}',
            method: 'POST',
            data: JSON.stringify(invoiceData),
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert('Invoice created successfully!');
                    window.location.href = '{{ route("sevdesk.index") }}';
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                let msg = xhr.responseJSON?.message || 'Unknown error';

                if (xhr.responseJSON?.error) {
                    const stockErrors = xhr.responseJSON.error;

                    if (Array.isArray(stockErrors)) {
                        const issues = stockErrors.map(issue =>
                            `• ${issue.issue}`
                        ).join('\n');
                        msg += '\n\nDetails:\n' + issues;
                    }
                }

                alert('Error creating invoice:\n\n' + msg);
            }
        });
    });
});
</script>
<script>
$(document).ready(function() {
    // Remove any existing event handlers to prevent duplicates
    $(document).off('keyup', 'input[name$="[product_name]"]');

    // REMOVED: Auto-close dropdown on outside clicks to prevent mobile UX issues
    // The dropdown will now only close when:
    // 1. A product is selected from the dropdown
    // 2. The user explicitly presses Escape key
    // 3. The user focuses on a different product search field
    // 4. The user clears the search input

    $(document).on('click', '.autocomplete-item', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Force remove dropdown as backup
        setTimeout(function() {
            $('.autocomplete-dropdown').remove();
        }, 100);
    });

    // Product name search with automatic item number population
    $(document).on('keyup', 'input[name$="[product_name]"]', function() {
        const $input = $(this);
        const term = $input.val().trim();

        if (term.length < 2) {
            $('.autocomplete-dropdown').remove();
            return;
        }

        const index = $input.attr('name').match(/\[(\d+)\]/)[1];
        const $itemContainer = $input.closest('.invoice-item');
        const $itemNumberField = $itemContainer.find('.item-number-hidden');
        const $priceField = $itemContainer.find('.item-price');
        const $priceGrossField = $itemContainer.find('.item-price-gross');

        $.ajax({
            url: '{{ route("product.search") }}',
            method: 'GET',
            data: {
                term: term,
                searchType: 'name'
            },
            success: function(data) {
                $('.autocomplete-dropdown').remove();

                if (data.length === 0) {
                    return;
                }

                const $dropdown = $('<div class="autocomplete-dropdown"></div>');

                // Position dropdown correctly with theme-aware styling
                const inputPos = $input.offset();
                const isDarkMode = document.documentElement.getAttribute('data-bs-theme') === 'dark';

                // Mobile-specific positioning and styling
                $dropdown.css({
                    position: 'absolute',
                    top: inputPos.top + $input.outerHeight(),
                    left: inputPos.left,
                    width: $input.outerWidth(),
                    maxHeight: '200px',
                    backgroundColor: isDarkMode ? '#2b2b2b' : 'white',
                    border: isDarkMode ? '1px solid #404040' : '1px solid #ccc',
                    borderRadius: '4px',
                    zIndex: 9999,
                    boxShadow: isDarkMode ? '0 4px 12px rgba(0,0,0,0.4)' : '0 2px 5px rgba(0,0,0,0.2)',
                    // Mobile scrolling enhancements
                    touchAction: 'auto',
                    WebkitOverflowScrolling:  'auto',
                    overflowY: 'scroll',
                    overflowX: 'hidden'
                });

                data.forEach(function(item) {
                    const $item = $('<div class="autocomplete-item"></div>');
                    $item.text(item.description + ' (Inventory: ' + item.stock + ')');

                    const itemStyles = {
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderBottom: isDarkMode ? '1px solid #404040' : '1px solid #eee',
                        color: isDarkMode ? '#e9ecef' : '#212529',
                        backgroundColor: isDarkMode ? '#2b2b2b' : 'white'
                    };

                    $item.css(itemStyles);

                    $item.hover(
                        function() {
                            $(this).css('backgroundColor', isDarkMode ? '#404040' : '#f0f0f0');
                        },
                        function() {
                            $(this).css('backgroundColor', isDarkMode ? '#2b2b2b' : 'white');
                        }
                    );

                    $item.on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        if(item.stock <= 0) {
                            alert('This product is out of stock.');
                            return;
                        }

                        try {
                            // Populate form fields - product name and hidden item number
                            $input.val(item.description);
                            $itemNumberField.val(item.itemNumber);
                            $priceField.val(item.priceNet);
                            $priceGrossField.val(item.priceGross);

                            // Set quantity field maximum value to available stock
                            const $quantityField = $itemContainer.find('.item-quantity');
                            $quantityField.attr('max', item.stock);

                            // If current quantity exceeds available stock, reset to maximum available
                            const currentQuantity = parseInt($quantityField.val()) || 1;
                            if (currentQuantity > item.stock) {
                                $quantityField.val(item.stock);
                            }

                            // Add visual indicator for stock limit
                            const $quantityLabel = $itemContainer.find('label:contains("Quantity")');
                            $quantityLabel.html(`Quantity <small class="text-muted">(Stock: ${item.stock})</small>`);
 
                        } catch (error) {
                            console.error('Error populating fields:', error);
                        }

                        // Remove dropdown immediately and with backup
                        $('.autocomplete-dropdown').remove();
                        setTimeout(function() {
                            $('.autocomplete-dropdown').remove();
                        }, 100);
                    });

                    $dropdown.append($item);
                });

                $('body').append($dropdown);
            },
            error: function(xhr) {
                console.error('Error searching products:', xhr);
            }
        });
    });

    // Ensure dropdown is removed when modal is closed
    $('#editOrderModal').on('hidden.bs.modal', function() {
        $('.autocomplete-dropdown').remove();
    });

    // Ensure dropdown is removed when clicking on other form elements
    $(document).on('focus', 'input, select, textarea, button', function() {
        if (!$(this).is('input[name$="[product_name]"]')) {
            $('.autocomplete-dropdown').remove();
        }
    });

    // REMOVED: Auto-close dropdown on blur to prevent mobile UX issues
    // On mobile, blur events can fire unexpectedly when the virtual keyboard appears/disappears
    // The dropdown will remain open until explicitly closed by user action

    // Close dropdown when Escape key is pressed
    $(document).on('keydown', 'input[name$="[product_name]"]', function(e) {
        if (e.key === 'Escape' || e.keyCode === 27) {
            $('.autocomplete-dropdown').remove();
            $(this).blur(); // Remove focus from input
        }
    });

    // Close dropdown when any button is clicked
    $(document).on('click', 'button', function() {
        $('.autocomplete-dropdown').remove();
    });

    // Close dropdown when form is submitted
    $(document).on('submit', 'form', function() {
        $('.autocomplete-dropdown').remove();
    });


    // Improved touch handling for mobile dropdown scrolling
    let isScrolling = false;
    let startY = 0;

    // Track scroll start
    $(document).on('touchstart', '.autocomplete-dropdown', function(e) {
        isScrolling = false;
        startY = e.originalEvent.touches[0].clientY;
        // Don't prevent default - allow scrolling
    });

    // Track if user is scrolling
    $(document).on('touchmove', '.autocomplete-dropdown', function(e) {
        const currentY = e.originalEvent.touches[0].clientY;
        const deltaY = Math.abs(currentY - startY);

        if (deltaY > 10) { // Threshold for scroll detection
            isScrolling = true;
        }
        // Don't prevent default - allow scrolling
    });

    // Handle item selection only if not scrolling
    $(document).on('touchend', '.autocomplete-item', function(e) {
        if (!isScrolling) {
            e.preventDefault();
            e.stopPropagation();
            $(this).trigger('click');
        }
        // Reset scrolling flag after a delay
        setTimeout(() => {
            isScrolling = false;
        }, 100);
    });

    // Prevent dropdown from closing during scroll
    $(document).on('touchmove', '.autocomplete-dropdown', function(e) {
        e.stopPropagation();
    });

    // Validate quantity input against stock limits
    $(document).on('input change', '.item-quantity', function() {
        const $quantityField = $(this);
        const maxStock = parseInt($quantityField.attr('max'));
        const currentQuantity = parseInt($quantityField.val()) || 0;

        if (maxStock && currentQuantity > maxStock) {
            // Show warning and reset to maximum available
            $quantityField.val(maxStock);

            // Show temporary warning message
            const $itemContainer = $quantityField.closest('.invoice-item');

            // Temporarily highlight the field
            $quantityField.addClass('is-invalid');
            setTimeout(function() {
                $quantityField.removeClass('is-invalid');
            }, 2000);

            // Show alert for user feedback
            alert(`Maximum available quantity is ${maxStock}`);
        }
    });

    // Reset quantity label and max attribute when product name is cleared
    $(document).on('input', 'input[name$="[product_name]"]', function() {
        const $input = $(this);
        const $itemContainer = $input.closest('.invoice-item');

        // If input is cleared, reset quantity constraints
        if ($input.val().trim() === '') {
            const $quantityField = $itemContainer.find('.item-quantity');
            const $quantityLabel = $itemContainer.find('label:contains("Quantity")');

            $quantityField.removeAttr('max');
            $quantityLabel.html('Quantity');
        }
    });
});
</script>
{% endblock %}
