{% extends 'html.twig' %}

{% block body %}
{% include 'component/sidebar.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12">
                    <div class="page-title-box">
                        <h4 class="page-title">Sevdesk Invoices</h4>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h4 class="card-title">All Invoices</h4>
                        </div>
                        <div class="col-auto">
                            <a href="{{ route('sevdesk.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Create New Invoice
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Invoice Number</th>
                                <th>Customer</th>
                                <th>Status</th>
                                <th>Total</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                            <tr>
                                <td>
                                    {% if invoice.is_template %}
                                        {{ invoice.invoice_number }}
                                    {% else %}
                                        {{ invoice.getInvoiceNumberFromResponse() }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if invoice.customer %}
                                        {{ invoice.customer.first_name }} {{ invoice.customer.last_name }}
                                    {% else %}
                                        <span class="text-muted"> - </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if invoice.is_template %}
                                        <span class="badge bg-secondary">Template</span>
                                    {% else %}
                                        <span class="badge bg-{{ invoice.getStatusBadgeClassFromResponse() }}">
                                            {{ invoice.getStatusLabelFromResponse() }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    €{{ invoice.getTotalFromResponse()|number_format(2, ',', '.') }}
                                </td>
                                <td>{{ invoice.created_at|date('d M Y') }}</td>
                                <td>
                                    {% if invoice.is_template %}
                                        <div class="btn-group gap-2">
                                            <a href="{{ route('sevdesk.template', {id: invoice.id}) }}" class="btn btn-sm btn-success">
                                            <i class="fas fa-copy me-1"></i>Use Draft
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger delete-invoice-btn" data-invoice-id="{{ invoice.id }}">
                                         <i class="fas fa-trash"></i>Delete</button>
                                        </div>
                                    {% else %}
                                            <div class="btn-group gap-2">
                                            <a href="{{ route('sevdesk.show', {id: invoice.id}) }}" class="btn btn-sm btn-info">View</a>
                                            {% if not invoice.is_cancelled %}
                                            <button type="button" class="btn btn-sm btn-warning cancel-invoice-btn" data-invoice-id="{{ invoice.id }}">
                                                <i class="fas fa-ban"></i> Cancel
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger delete-invoice-btn" data-invoice-id="{{ invoice.id }}">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                            {% endif %}
                                            </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // CSRF Token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    });

    // Cancel Invoice confirmation and AJAX request
    $('.cancel-invoice-btn').click(function() {
        const invoiceId = $(this).data('invoice-id');

        if (!confirm('Are you sure you want to cancel this invoice? This action cannot be undone.')) {
            return;
        }

        const button = $(this);
        const originalText = button.html();

        // Disable button and show loading state
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Cancelling...');

        $.ajax({
            url: '/sevdesk/' + invoiceId + '/cancel',
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    alert('Invoice cancelled successfully!');
                    // Reload the page to reflect changes
                    window.location.reload();
                } else {
                    alert('Error: ' + response.message);
                    // Restore button state
                    button.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Unknown error occurred';
                alert('Error cancelling invoice: ' + message);
                // Restore button state
                button.prop('disabled', false).html(originalText);
            }
        });
    });

    // Delete Invoice confirmation and AJAX request
    $('.delete-invoice-btn').click(function() {
        const invoiceId = $(this).data('invoice-id');

        if (!confirm('Are you sure you want to delete this invoice? This action will permanently remove the invoice and cannot be undone.')) {
            return;
        }

        const button = $(this);
        const originalText = button.html();

        // Disable button and show loading state
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Deleting...');

        $.ajax({
            url: '/sevdesk/' + invoiceId + '/destroy',
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    alert('Invoice deleted successfully!');
                    // Reload the page to reflect changes
                    window.location.reload();
                } else {
                    alert('Error: ' + response.message);
                    // Restore button state
                    button.prop('disabled', false).html(originalText);
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Unknown error occurred';
                alert('Error deleting invoice: ' + message);
                // Restore button state
                button.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script>
{% endblock %}
