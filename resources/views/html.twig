<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Portal Nurederm</title>
    <link href="/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="/icons.min.css" rel="stylesheet" type="text/css" />
    <link href="/app.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Tema flash'ını önlemek için sayfa yüklenmeden önce tema ayarlanır
        if (localStorage.getItem('theme')) {
            document.documentElement.setAttribute('data-bs-theme', localStorage.getItem('theme'));
        } else {
            document.documentElement.setAttribute('data-bs-theme', 'light');
        }
    </script>
    <style>
        .startbar .startbar-menu .navbar-nav .nav-item .nav-link[data-bs-toggle=collapse]:after {
            display: block;
            content: "\f054";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            margin-left: auto;
            line-height: 20px;
            -webkit-transition: -webkit-transform .2s;
            transition: -webkit-transform .2s;
            transition: transform .2s;
            transition: transform .2s, -webkit-transform .2s;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "\f101";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
        }
    </style>

</head>
<body>
    {% include 'component/navbar.twig' %}
    {% block body %}
    {% endblock %}

    {# Önce temel kütüphaneler #}
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    {# Sonra diğer scriptler #}
    <script src="/simplebar.min.js"></script>
    <script src="/apexcharts.min.js"></script>
    <script src="/index.init.js"></script>
    <script src="/app.js"></script>

    {# En son sayfa özel scriptleri #}
    {% block javascripts %}{% endblock %}
</body>
</html>


