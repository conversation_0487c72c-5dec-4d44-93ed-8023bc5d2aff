{% include 'html.twig' %}

{% block body %}

<!-- Autocomplete CSS -->
<link href="{{ asset('css/autocomplete.css') }}" rel="stylesheet" type="text/css" />

{% include 'component/sidebar.twig' %}
{% include 'component/excel_import_modal.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12">
                    <div class="page-title-box d-md-flex justify-content-md-between align-items-center">
                        <h4 class="page-title">Create Inbound</h4>
                        <div>
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="#">Products</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('inbound.index') }}">Inbounds</a></li>
                                <li class="breadcrumb-item active">Create</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <form id="createInboundForm">
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                
                                <!-- Inbound Date -->
                                <div class="mb-3">
                                    <label class="form-label">Inbound Date</label>
                                    <input type="date" class="form-control" id="inboundDate" name="inbound_date" value="{{ 'now'|date('Y-m-d') }}">
                                </div>
                                
                                <!-- Inbound Type -->
                                <div class="mb-3">
                                    <label class="form-label">Inbound Type</label>
                                    <select class="form-select" id="inboundType" name="type">
                                        <option value="new_stock">New Stock</option>
                                        <option value="return">Return</option>
                                    </select>
                                </div>
                                
                                <!-- Items Section -->
                                <div class="border p-3 rounded mb-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <h6>Items</h6>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-info me-2" data-bs-toggle="modal" data-bs-target="#excelInfoModal">
                                                <i class="fas fa-question-circle"></i>
                                            </button>
                                            <label for="excelImport" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-file-excel me-1"></i> Import Excel
                                            </label>
                                            <input type="file" id="excelImport" class="d-none" accept=".xlsx,.xls">
                                        </div>
                                    </div>
                                    <div id="itemsContainer">
                                        <!-- Items will be added here -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="addItemBtn">
                                        <i class="fas fa-plus me-1"></i> Add Item
                                    </button>
                                </div>
                                
                                <div class="text-end mt-3">
                                    <a href="{{ route('inbound.index') }}" class="btn btn-secondary me-2">Cancel</a>
                                    <button type="button" class="btn btn-primary" id="saveInboundBtn">Create Inbound</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script src="{{ asset('js/inbound.js') }}"></script>
<script>
$(document).ready(function() {
    // Initialize inbound form
    initInboundForm({
        mode: 'create'
    });
    
    // Save inbound
    $('#saveInboundBtn').click(function(e) {
        e.preventDefault();
        
        // Form validation
        if (!$('#createInboundForm')[0].checkValidity()) {
            $('#createInboundForm')[0].reportValidity();
            return;
        }
        
        // Prepare form data
        const formData = {
            inbound_date: $('#inboundDate').val(),
            type: $('#inboundType').val(),
            items: []
        };
        
        // Get all items
        $('.item-row').each(function() {
            const row = $(this);
            const productNumber = row.find('.product-number').val().trim();
            const quantity = row.find('.item-quantity').val();
            
            if (productNumber && quantity) {
                formData.items.push({
                    productNumber: productNumber,
                    quantity: quantity,
                });
            }
        });
        
        // Send AJAX request
        $.ajax({
            url: '{{ route("inbound.store") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val()
            },
            data: JSON.stringify(formData),
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    window.location.href = '{{ route("inbound.index") }}';
                }
            },
            error: function(xhr) {
                const message = xhr.responseJSON?.message || 'Unknown error';
                alert('Error creating inbound: ' + message);
                console.error('Error:', xhr);
            }
        });
    });
});
</script>
{% endblock %}
