{% include 'html.twig' %}

{% block body %}


<style>
    @media (max-width: 600px) {

    .card-header .row {
        display: flex;
        flex-direction: column !important;
        justify-content: center;
    }

    .card-header .card-title {
        text-align: center;
    }

    .card-header .btn-primary {
        margin-top: 10px;
    }
}

.delete-invoice i{
    cursor: pointer;
    margin-left: 5px;
}

</style>

{% include 'component/sidebar.twig' %}

        <div class="page-wrapper">

            <!-- Page Content-->
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="page-title-box d-md-flex justify-content-md-between align-items-center">
                                <h4 class="page-title">Inbounds</h4>
                                <div="">
                                    <ol class="breadcrumb mb-0">
                                        <li class="breadcrumb-item"><a href="#">Products</a>
                                        </li><!--end nav-item-->
                                        <li class="breadcrumb-item active">Inbounds</li>
                                    </ol>
                                </div>
                            </div><!--end page-title-box-->
                        </div><!--end col-->
                    </div><!--end row-->
                    <div class="row mb-4">
                        <div class="col-lg-12">
                            <div class="card bg-primary">
                                <div class="card-body text-center py-4 d-flex justify-content-center align-items-center">
                                    <h1 style="font-size: clamp(24px, 4vw, 44px); color: white; font-weight: bold;" class="mb-0">Sevkdesk Inbound Management</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Orders Card -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h4 class="card-title">Inbounds</h4>
                                        </div><!--end col-->
                                        <div class="col-auto">
                                            <a href="{{ route('inbound.create') }}" class="btn btn-primary">
                                                <i class="fas fa-plus me-1"></i> Add Inbound
                                            </a>
                                        </div><!--end col-->
                                    </div>  <!--end row-->
                                </div><!--end card-header-->
                                <div class="card-body pt-0">
                                    <div class="table-responsive">
                                        <table class="table mb-0">
                                            <thead class="table-light">
                                              <tr>
                                                <th>Inbound ID</th>
                                                <th>Date</th>
                                                <th>Status</th>
                                                <th>Type</th>
                                                <th class="text-end">Action</th>
                                              </tr>
                                            </thead>
                                            <tbody>
                                                {% for inbound in inbounds %}
                                                <tr>
                                                    <td><a href="{{ route('inbound.edit', {'id': inbound.id}) }}">#{{ inbound.id }}</a></td>
                                                    <td>{{ inbound.inbound_date|date('d/m/Y') }}</td>
                                                    <td>
                                                        <span class="{{ inbound.statusEnum.badgeClass }}">
                                                            <i class="{{ inbound.statusEnum.iconClass }}"></i> {{ inbound.statusEnum.label }}
                                                        </span>
                                                    </td>
                                                     <td>
                                                        <span class="{{ inbound.typeEnum.badgeClass }}">
                                                            <i class="{{ inbound.typeEnum.iconClass }}"></i> {{ inbound.typeEnum.label }}
                                                        </span>
                                                    </td>

                                                    <td class="text-end">
                                                        <a href="{{ route('inbound.edit', {'id': inbound.id}) }}"><i class="fas fa-pen text-secondary fs-18"></i></a>
                                                        <a href="#" class="delete-invoice" data-id="{{ inbound.id }}"><i class="fas fa-trash-alt text-secondary fs-18"></i></a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- end col -->
                    </div> <!-- end row -->
                </div><!-- container -->

            </div>
            <!-- end page content -->
        </div>
        <!-- Create Order Modal -->
        <div class="modal fade" id="createOrderModal" tabindex="-1" aria-labelledby="createOrderModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="createOrderModalLabel">Create New Order</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createOrderForm">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <!-- Invoice Date -->
                            <div class="mb-3">
                                <label class="form-label">Invoice Date</label>
                                <input type="date" class="form-control" id="invoiceDate" value="{{ 'now'|date('Y-m-d') }}">
                            </div>
                            <!-- Items Section -->
                            <div class="border p-3 rounded mb-3">
                                <h6>Items</h6>
                                <div id="itemsContainer">
                                    <!-- Items will be added here -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="addItemBtn">
                                    <i class="fas fa-plus me-1"></i> Add Item
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" id="saveOrderBtn">Create Order</button>
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Delete inbound
    $('.delete-invoice').click(function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to delete this inbound?')) {
            return;
        }
        
        const inboundId = $(this).data('id');
        
        $.ajax({
            url: '{{ route("inbound.destroy", {"id": "PLACEHOLDER"}) }}'.replace('PLACEHOLDER', inboundId),
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    window.location.reload();
                }
            },
            error: function(xhr) {
                alert('Error deleting inbound: ' + (xhr.responseJSON?.message || 'Unknown error'));
            }
        });
    });
});
</script>
{% endblock %}
