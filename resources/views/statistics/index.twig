{% extends 'html.twig' %}

{% block body %}

<style>
    .card-header .d-flex {
        width: 100%;
        justify-content: center !important;
        align-items: center !important;
    }

    .card-header .gap-2 {
       margin-top: 1rem;
    }
</style>


{% include 'component/sidebar.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <!-- Stock Management Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm border-info">
                        <div class="card-header bg-info bg-gradient text-white">
                            <div class="d-flex justify-content-between align-items-center flex-wrap">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-warehouse me-2"></i>Stock Management
                                </h5>
                                <div class="d-flex gap-2 align-items-center flex-wrap">
                                    <!-- Daily Stock Navigation -->
                                    <div class="d-flex gap-1 align-items-center">
                                        <small class="text-white-50 me-2">Daily View:</small>
                                        <button type="button" class="btn btn-outline-light btn-sm" id="prevStockDay">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                        <div class="input-group" style="max-width: 160px;">
                                            <span class="input-group-text bg-white">
                                                <i class="fas fa-calendar text-info"></i>
                                            </span>
                                            <input type="date" class="form-control form-control-sm" id="stockDatePicker"
                                                   value="{{ stockDate }}" title="Select stock date">
                                        </div>
                                        <button type="button" class="btn btn-outline-light btn-sm" id="nextStockDay">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>

                                    <!-- Stock Range Analysis -->
                                    <div class="d-flex gap-1 align-items-center ms-3">
                                        <small class="text-white-50 me-2">Range Analysis:</small>
                                        <form method="GET" action="{{ route('statistics.index') }}" class="d-flex gap-1 align-items-center">
                                            <!-- Preserve sales date values -->
                                            <input type="hidden" name="start_date" value="{{ startDate }}">
                                            <input type="hidden" name="end_date" value="{{ endDate }}">

                                            <div class="input-group" style="max-width: 140px;">
                                                <span class="input-group-text bg-white text-muted" style="font-size: 0.75rem;">From</span>
                                                <input type="date" class="form-control form-control-sm" name="stock_start_date"
                                                       value="{{ stockStartDate }}" title="Stock range start date">
                                            </div>
                                            <div class="input-group" style="max-width: 140px;">
                                                <span class="input-group-text bg-white text-muted" style="font-size: 0.75rem;">To</span>
                                                <input type="date" class="form-control form-control-sm" name="stock_end_date"
                                                       value="{{ stockEndDate }}" title="Stock range end date">
                                            </div>
                                            <button type="submit" class="btn btn-outline-light btn-sm" title="Update stock range analysis">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Daily Stock Summary Cards -->
                            <div class="row mb-3">
                                <div class="col-lg-3 col-md-6">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-info bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-boxes text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Total Products</p>
                                                    <h4 class="mb-0 text-info" id="stockTotalProducts">0</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-success bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-euro-sign text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Total Stock Value</p>
                                                    <h4 class="mb-0 text-success" id="stockTotalValue">€0,00</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-warning bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-calendar text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Selected Date</p>
                                                    <h4 class="mb-0 text-warning" id="stockSelectedDate">{{ stockDate|date('M d, Y') }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6">
                                    <div class="card bg-light border-0">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-primary bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-exchange-alt text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Daily Changes</p>
                                                    <h4 class="mb-0 text-primary" id="stockDailyChanges">0</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Daily Stock Data Table -->
                            <div class="card border-0">
                                <div class="card-header bg-light d-flex justify-content-between align-items-center flex-wrap">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-table me-2"></i>Stock Levels
                                    </h6>
                                    <div class="d-flex gap-2 flex-wrap">
                                        <input type="text" class="form-control form-control-sm" id="searchStockTable"
                                               placeholder="Search products..." style="width: 150px;">
                                        <select class="form-select form-select-sm" id="stockLevelFilter" style="width: 120px;">
                                            <option value="">All Levels</option>
                                            <option value="critical">Critical (0)</option>
                                            <option value="low">Low (1-5)</option>
                                            <option value="good">Good (6+)</option>
                                        </select>
                                        <button class="btn btn-outline-info btn-sm" id="exportStockData">
                                            <i class="fas fa-download me-1"></i>Export
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-hover align-middle mb-0" id="stockDataTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th class="sortable-stock">Product #</th>
                                                    <th class="sortable-stock">Description</th>
                                                    <th class="sortable-stock text-center">Current Stock</th>
                                                    <th class="sortable-stock text-center">Previous Stock</th>
                                                    <th class="sortable-stock text-center">Daily Change</th>
                                                    <th class="sortable-stock text-end">Sales Price</th>
                                                    <th class="sortable-stock text-end">Cost Price</th>
                                                    <th class="sortable-stock text-end">Stock Value</th>
                                                </tr>
                                            </thead>
                                            <tbody id="stockTableBody">
                                                <!-- Data will be populated by JavaScript -->
                                            </tbody>
                                        </table>
                                        <div id="noStockDataMessage" class="text-center py-5 d-none">
                                            <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">No stock data available</h5>
                                            <p class="text-muted">No stock information found for the selected date</p>
                                        </div>
                                    </div>

                                    <!-- Pagination Controls -->
                                    <div class="card-footer bg-light" id="stockPaginationContainer">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="text-muted">Show:</span>
                                                <select class="form-select form-select-sm" id="stockItemsPerPage" style="width: auto;">
                                                    <option value="25">25</option>
                                                    <option value="50" selected>50</option>
                                                    <option value="100">100</option>
                                                    <option value="all">All</option>
                                                </select>
                                                <span class="text-muted">items per page</span>
                                            </div>

                                            <div class="d-flex align-items-center gap-2">
                                                <span class="text-muted" id="stockPaginationInfo">Showing 1-50 of 100</span>
                                            </div>

                                            <nav aria-label="Stock table pagination">
                                                <ul class="pagination pagination-sm mb-0" id="stockPagination">
                                                    <li class="page-item disabled" id="stockPrevPage">
                                                        <a class="page-link" href="#" aria-label="Previous">
                                                            <span aria-hidden="true">&laquo;</span>
                                                        </a>
                                                    </li>
                                                    <li class="page-item active" id="stockCurrentPage">
                                                        <a class="page-link" href="#">1</a>
                                                    </li>
                                                    <li class="page-item" id="stockNextPage">
                                                        <a class="page-link" href="#" aria-label="Next">
                                                            <span aria-hidden="true">&raquo;</span>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stock Range Analysis Section -->
            {% if stockRangeAggregation is defined %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm border-success">
                        <div class="card-header bg-success bg-gradient text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-area me-2"></i>Stock Range Analysis
                                <small class="ms-2 opacity-75">
                                    ({{ stockRangeAggregation.dateRange.start|date('M d, Y') }} - {{ stockRangeAggregation.dateRange.end|date('M d, Y') }})
                                </small>
                            </h5>
                        </div>
                        <div class="card-body">
                            <!-- Stock Range Summary Cards -->
                            <div class="row">
                                <div class="col-xl-3 col-md-6">
                                    <div class="card bg-light border-0 h-100">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-primary bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-play text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Starting Date Stock</p>
                                                    <h4 class="mb-0 text-primary">{{ stockRangeAggregation.startingStock.totalQuantity|number_format }}</h4>
                                                    <small class="text-muted">€{{ stockRangeAggregation.startingStock.totalValue|number_format(2, ',', '.') }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6">
                                    <div class="card bg-light border-0 h-100">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-info bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-stop text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Ending Date Stock</p>
                                                    <h4 class="mb-0 text-info">{{ stockRangeAggregation.endingStock.totalQuantity|number_format }}</h4>
                                                    <small class="text-muted">€{{ stockRangeAggregation.endingStock.totalValue|number_format(2, ',', '.') }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6">
                                    <div class="card bg-light border-0 h-100">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-danger bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-shopping-cart text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Products Sold During This Period</p>
                                                    <h4 class="mb-0 text-danger">{{ stockRangeAggregation.totalProductsSold.totalQuantity|number_format }}</h4>
                                                    <small class="text-muted">€{{ stockRangeAggregation.totalProductsSold.totalValue|number_format(2, ',', '.') }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6">
                                    <div class="card bg-light border-0 h-100">
                                        <div class="card-body text-center">
                                            <div class="d-flex align-items-center justify-content-center">
                                                <div class="flex-shrink-0">
                                                    <div class="avatar-sm rounded-circle bg-success bg-gradient">
                                                        <span class="avatar-title rounded-circle">
                                                            <i class="fas fa-arrow-up text-white"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3 text-start">
                                                    <p class="text-muted mb-1 fw-semibold">Inbound Stock During This Period</p>
                                                    <h4 class="mb-0 text-success">{{ stockRangeAggregation.totalInboundStock.totalQuantity|number_format }}</h4>
                                                    <small class="text-muted">€{{ stockRangeAggregation.totalInboundStock.totalValue|number_format(2, ',', '.') }}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <div class="row">
                <div class="col-sm-12">
                    <div class="page-title-box d-md-flex justify-content-md-between align-items-center">
                        <h4 class="page-title">
                            <i class="fas fa-chart-line me-2"></i>Daily Sales Statistics
                        </h4>
                        <div>
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="#">Dashboard</a></li>
                                <li class="breadcrumb-item active">Statistics</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
                  <!-- Sales Date Range Filter -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-line me-2"></i>Sales Date Range Filter
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="{{ route('statistics.index') }}" class="row g-3 align-items-end">
                                <!-- Sales Date Range -->
                                <div class="col-md-4">
                                    <label for="start_date" class="form-label fw-semibold">
                                        <i class="fas fa-calendar-alt me-1"></i>Start Date
                                    </label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ startDate }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="end_date" class="form-label fw-semibold">
                                        <i class="fas fa-calendar-alt me-1"></i>End Date
                                    </label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ endDate }}">
                                </div>
                                <div class="col-md-4">
                                    <button type="submit" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-search me-2"></i>Filter Sales Data
                                    </button>
                                </div>

                                <!-- Hidden stock date inputs to preserve values -->
                                <input type="hidden" name="stock_start_date" value="{{ stockStartDate }}">
                                <input type="hidden" name="stock_end_date" value="{{ stockEndDate }}">
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- View Toggle and Navigation -->
            {% if dailyData|length > 0 %}
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-body">
                            <!-- View Toggle Controls -->
                            <div class="d-flex justify-content-center mb-3">
                                <div class="btn-group" role="group" aria-label="View Toggle">
                                    <input type="radio" class="btn-check" name="viewToggle" id="dailyViewToggle" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary" for="dailyViewToggle">
                                        <i class="fas fa-calendar-day me-2"></i>Daily View
                                    </label>

                                    <input type="radio" class="btn-check" name="viewToggle" id="rangeViewToggle" autocomplete="off">
                                    <label class="btn btn-outline-primary" for="rangeViewToggle">
                                        <i class="fas fa-chart-line me-2"></i>Range Summary
                                    </label>
                                </div>
                            </div>

                            <!-- Daily Navigation Controls -->
                            <div id="dailyNavigationControls">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-calendar-day me-2"></i>Daily View
                                    </h5>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-outline-primary" id="prevDay">
                                            <i class="fas fa-chevron-left"></i> Previous
                                        </button>
                                        <button type="button" class="btn btn-primary" id="currentDate">
                                            {{ dailyData[0].date|date('M d, Y') }}
                                        </button>
                                        <button type="button" class="btn btn-outline-primary" id="nextDay">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Range Summary Controls -->
                            <div id="rangeSummaryControls" class="d-none">
                                <div class="text-center">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-line me-2"></i>Range Summary
                                    </h5>
                                    <small class="text-muted" id="rangeSummaryInfo">
                                        Aggregated statistics for the selected period
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Daily Summary Cards -->
            <div class="row mb-4" id="dailySummary">
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-primary bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-shopping-cart text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Total Units Sold</p>
                                    <h4 class="mb-0" id="totalSoldValue">0</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-success bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-euro-sign text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Total Sales Value</p>
                                    <h4 class="mb-0 text-success" id="totalValueValue">€0.00</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-info bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-calculator text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Average Price / Unit</p>
                                    <h4 class="mb-0 text-info" id="avgPriceValue">€0.00</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-warning bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-boxes text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Unique Products</p>
                                    <h4 class="mb-0 text-warning" id="uniqueProductsValue">0</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Range Summary Cards -->
            <div class="row mb-4 d-none" id="rangeSummary">
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-primary bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-shopping-cart text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Total Units Sold</p>
                                    <h4 class="mb-0" id="rangeTotalSoldValue">0</h4>
                                    <small class="text-muted" id="rangeTotalSoldSubtext">Across all days</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-success bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-euro-sign text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Total Sales Value</p>
                                    <h4 class="mb-0 text-success" id="rangeTotalValueValue">€0,00</h4>
                                    <small class="text-muted" id="rangeTotalValueSubtext">Period total</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-info bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-chart-line text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Avg Daily Sales</p>
                                    <h4 class="mb-0 text-info" id="rangeAvgDailySalesValue">0</h4>
                                    <small class="text-muted" id="rangeAvgDailySalesSubtext">Units per day</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="card border-0 shadow-sm h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="flex-shrink-0">
                                    <div class="avatar-sm rounded-circle bg-warning bg-gradient">
                                        <span class="avatar-title rounded-circle">
                                            <i class="fas fa-boxes text-white fs-4"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="text-muted mb-1 fw-semibold">Unique Products</p>
                                    <h4 class="mb-0 text-warning" id="rangeUniqueProductsValue">0</h4>
                                    <small class="text-muted" id="rangeUniqueProductsSubtext">In period</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar me-2"></i>Sales Overview
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="salesChart" style="height: 350px;"></div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card shadow-sm">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-pie me-2"></i>Top Products
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="topProductsChart" style="height: 350px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Data Table -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-table me-2"></i>Detailed Sales Data
                            </h5>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control form-control-sm" id="searchTable" placeholder="Search products..." style="width: 200px;">
                                <button class="btn btn-outline-primary btn-sm" id="exportData">
                                    <i class="fas fa-download me-1"></i>Export
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle" id="salesDataTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="sortable">Product #</th>
                                            <th class="sortable">Description</th>
                                            <th class="sortable text-center">Previous Stock</th>
                                            <th class="sortable text-center">Current Stock</th>
                                            <th class="sortable text-center">Inbounds</th>
                                            <th class="sortable text-center">Units Sold</th>
                                            <th class="sortable text-end">Price</th>
                                            <th class="sortable text-end">Value</th>
                                            <th class="sortable text-center">Sold %</th>
                                        </tr>
                                    </thead>
                                    <tbody id="salesTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                                <div id="noDataMessage" class="text-center py-5 d-none">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No sales data available</h5>
                                    <p class="text-muted">Try selecting a different date range</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Range Summary Table -->
            <div class="row mb-4 d-none" id="rangeSummaryTable">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-table me-2"></i>Range Summary - Product Sales
                            </h5>
                            <div class="d-flex gap-2">
                                <input type="text" class="form-control form-control-sm" id="searchRangeTable" placeholder="Search products..." style="width: 200px;">
                                <button class="btn btn-outline-primary btn-sm" id="exportRangeData">
                                    <i class="fas fa-download me-1"></i>Export Range
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle" id="rangeSalesDataTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="sortable">Product #</th>
                                            <th class="sortable">Description</th>
                                            <th class="sortable text-center">Total Units Sold</th>
                                            <th class="sortable text-end">Total Sales Value</th>
                                            <th class="sortable text-end">Avg Price</th>
                                            <th class="sortable text-center">Days Active</th>
                                        </tr>
                                    </thead>
                                    <tbody id="rangeSalesTableBody">
                                        <!-- Data will be populated by JavaScript -->
                                    </tbody>
                                </table>
                                <div id="noRangeDataMessage" class="text-center py-5 d-none">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No aggregated data available</h5>
                                    <p class="text-muted">Try selecting a different date range</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Products Section -->
            <div class="row mb-4">
                <div class="col-lg-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trophy me-2 text-warning"></i>Top Products by Units Sold
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="topSoldProductsList">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-medal me-2 text-success"></i>Top Products by Sales Value
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="topValueProductsList">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Range Summary Top Products Section -->
            <div class="row mb-4 d-none" id="rangeTopProducts">
                <div class="col-lg-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-trophy me-2 text-warning"></i>Top Products by Total Units Sold
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="rangeTopSoldProductsList">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card shadow-sm h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-medal me-2 text-success"></i>Top Products by Total Sales Value
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="rangeTopValueProductsList">
                                <!-- Will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="d-none">
    <div class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(0,0,0,0.5); z-index: 9999;">
        <div class="text-center text-white">
            <div class="spinner-border mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h5>Loading sales data...</h5>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<style>
    .avatar-sm {
        height: 3rem;
        width: 3rem;
    }
    .avatar-title {
        align-items: center;
        background-color: transparent;
        color: #6c757d;
        display: flex;
        font-weight: 500;
        height: 100%;
        justify-content: center;
        width: 100%;
    }
    .sortable {
        cursor: pointer;
        user-select: none;
    }
    .sortable:hover {
        background-color: #f8f9fa;
    }
    .sort-asc::after {
        content: " ↑";
        color: #007bff;
    }
    .sort-desc::after {
        content: " ↓";
        color: #007bff;
    }
    .product-item {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .product-item:hover {
        border-left-color: #007bff;
        background-color: #f8f9fa;
    }
    .badge-rank {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .sortable-stock {
        cursor: pointer;
        user-select: none;
        position: relative;
    }

    .sortable-stock:hover {
        background-color: #f8f9fa;
    }

    .sortable-stock.sort-asc::after {
        content: " ↑";
        color: #0d6efd;
        font-weight: bold;
    }

    .sortable-stock.sort-desc::after {
        content: " ↓";
        color: #0d6efd;
        font-weight: bold;
    }

    .border-info {
        border-color: #0dcaf0 !important;
    }

    .stock-change-increase {
        color: #198754;
    }

    .stock-change-decrease {
        color: #dc3545;
    }

    .stock-level-critical {
        background-color: #f8d7da;
        color: #721c24;
    }

    .stock-level-low {
        background-color: #fff3cd;
        color: #856404;
    }

    .stock-level-good {
        background-color: #d1e7dd;
        color: #0f5132;
    }

    @media (max-width: 768px) {
        .btn-group .btn {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }

        .card-body {
            padding: 1rem;
        }

        .table-responsive {
            font-size: 0.875rem;
        }

        .product-item {
            padding: 0.75rem !important;
        }

        .product-item .badge {
            font-size: 0.75rem;
        }

        .avatar-sm {
            height: 2.5rem;
            width: 2.5rem;
        }

        .fs-4 {
            font-size: 1rem !important;
        }
    }

    @media (max-width: 576px) {
        .page-title {
            font-size: 1.25rem;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn-group .btn {
            border-radius: 0.375rem !important;
            margin-bottom: 0.25rem;
        }

        .btn-group .btn:last-child {
            margin-bottom: 0;
        }
    }
</style>

<script>
// Global variables
let dailyData = [];
let rangeAggregation = {};
let stockData = {};
let currentDayIndex = 0;
let currentView = 'daily'; // 'daily' or 'range'
let currentStockDate = '';
let stockCurrentPage = 1;
let stockItemsPerPage = 50;
let stockFilteredData = [];
let stockCurrentSort = { column: 0, direction: 'asc' };
let salesChart = null;
let topProductsChart = null;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Get data from the controller
    {% if dailyData is defined and dailyData|length > 0 %}
        dailyData = {{ dailyData|json_encode|raw }};
    {% else %}
        dailyData = [];
    {% endif %}

    {% if rangeAggregation is defined %}
        rangeAggregation = {{ rangeAggregation|json_encode|raw }};
    {% else %}
        rangeAggregation = {};
    {% endif %}

    {% if stockData is defined %}
        stockData = {{ stockData|json_encode|raw }};
    {% else %}
        stockData = {};
    {% endif %}

    {% if stockDate is defined %}
        currentStockDate = '{{ stockDate }}';
    {% else %}
        currentStockDate = new Date().toISOString().split('T')[0];
    {% endif %}

    initializeCharts();
    updateDisplay();
    updateStockDisplay();
    bindEvents();
});

function initializeCharts() {
    // Sales Overview Chart
    const salesChartOptions = {
        series: [{
            name: 'Units Sold',
            data: []
        }, {
            name: 'Sales Value (€)',
            data: []
        }],
        chart: {
            type: 'line',
            height: 350,
            toolbar: {
                show: true
            }
        },
        colors: ['#007bff', '#28a745'],
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 3
        },
        xaxis: {
            categories: [],
            title: {
                text: 'Date'
            }
        },
        yaxis: [{
            title: {
                text: 'Units Sold'
            },
            labels: {
                formatter: function(value) {
                    return formatNumber(value);
                }
            }
        }, {
            opposite: true,
            title: {
                text: 'Sales Value (€)'
            },
            labels: {
                formatter: function(value) {
                    return formatCurrency(value);
                }
            }
        }],
        tooltip: {
            y: [{
                formatter: function(value) {
                    return formatNumber(value) + ' units';
                }
            }, {
                formatter: function(value) {
                    return formatCurrency(value);
                }
            }]
        },
        legend: {
            position: 'top'
        },
        grid: {
            borderColor: '#f1f1f1'
        }
    };

    salesChart = new ApexCharts(document.querySelector("#salesChart"), salesChartOptions);
    salesChart.render();

    // Top Products Pie Chart
    const topProductsChartOptions = {
        series: [],
        chart: {
            type: 'donut',
            height: 350
        },
        labels: [],
        colors: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1'],
        legend: {
            position: 'bottom'
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '60%'
                }
            }
        },
        tooltip: {
            y: {
                formatter: function(value) {
                    return formatNumber(value) + ' units';
                }
            }
        },
        dataLabels: {
            formatter: function(val, opts) {
                return formatNumber(opts.w.config.series[opts.seriesIndex]);
            }
        }
    };

    topProductsChart = new ApexCharts(document.querySelector("#topProductsChart"), topProductsChartOptions);
    topProductsChart.render();
}

function bindEvents() {
    // Stock date navigation handlers
    document.getElementById('stockDatePicker')?.addEventListener('change', function(e) {
        loadStockDataForDate(e.target.value);
    });

    document.getElementById('prevStockDay')?.addEventListener('click', function() {
        const currentDate = new Date(currentStockDate);
        currentDate.setDate(currentDate.getDate() - 1);
        const newDate = currentDate.toISOString().split('T')[0];
        document.getElementById('stockDatePicker').value = newDate;
        loadStockDataForDate(newDate);
    });

    document.getElementById('nextStockDay')?.addEventListener('click', function() {
        const currentDate = new Date(currentStockDate);
        currentDate.setDate(currentDate.getDate() + 1);
        const newDate = currentDate.toISOString().split('T')[0];
        document.getElementById('stockDatePicker').value = newDate;
        loadStockDataForDate(newDate);
    });

    // Stock table search
    document.getElementById('searchStockTable')?.addEventListener('input', function(e) {
        filterStockTable(e.target.value);
    });

    // Stock level filter
    document.getElementById('stockLevelFilter')?.addEventListener('change', function(e) {
        filterStockByLevel(e.target.value);
    });

    // Stock table sorting
    document.querySelectorAll('.sortable-stock').forEach(header => {
        header.addEventListener('click', function() {
            sortStockTable(this);
        });
    });

    // Stock export functionality
    document.getElementById('exportStockData')?.addEventListener('click', function() {
        exportStockToCSV();
    });

    // Stock pagination handlers
    document.getElementById('stockItemsPerPage')?.addEventListener('change', function(e) {
        stockItemsPerPage = e.target.value === 'all' ? 'all' : parseInt(e.target.value);
        stockCurrentPage = 1;
        updateStockTableDisplay();
    });

    document.getElementById('stockPrevPage')?.addEventListener('click', function(e) {
        e.preventDefault();
        if (stockCurrentPage > 1) {
            stockCurrentPage--;
            updateStockTableDisplay();
        }
    });

    document.getElementById('stockNextPage')?.addEventListener('click', function(e) {
        e.preventDefault();
        const totalPages = Math.ceil(stockFilteredData.length / stockItemsPerPage);
        if (stockCurrentPage < totalPages) {
            stockCurrentPage++;
            updateStockTableDisplay();
        }
    });

    // View toggle handlers
    document.getElementById('dailyViewToggle')?.addEventListener('change', function() {
        if (this.checked) {
            currentView = 'daily';
            switchView();
        }
    });

    document.getElementById('rangeViewToggle')?.addEventListener('change', function() {
        if (this.checked) {
            currentView = 'range';
            switchView();
        }
    });

    // Day navigation
    document.getElementById('prevDay')?.addEventListener('click', function() {
        if (currentDayIndex > 0) {
            currentDayIndex--;
            updateDisplay();
        }
    });

    document.getElementById('nextDay')?.addEventListener('click', function() {
        if (currentDayIndex < dailyData.length - 1) {
            currentDayIndex++;
            updateDisplay();
        }
    });

    // Table search
    document.getElementById('searchTable')?.addEventListener('input', function(e) {
        filterTable(e.target.value);
    });

    document.getElementById('searchRangeTable')?.addEventListener('input', function(e) {
        filterRangeTable(e.target.value);
    });

    // Table sorting
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', function() {
            if (currentView === 'daily') {
                sortTable(this);
            } else {
                sortRangeTable(this);
            }
        });
    });

    // Export functionality
    document.getElementById('exportData')?.addEventListener('click', function() {
        exportToCSV();
    });

    document.getElementById('exportRangeData')?.addEventListener('click', function() {
        exportRangeToCSV();
    });
}

function showLoading() {
    // Simple loading indicator - could be enhanced with a proper spinner
    // For now, we'll just rely on the browser's loading state
}

function loadStockDataForDate(date) {
    showLoading();

    // Build URL with stock date parameter
    const url = new URL(window.location.href);
    url.searchParams.set('stock_date', date);

    // Redirect to load new stock data
    window.location.href = url.toString();
}

function updateStockDisplay() {
    if (!stockData || !stockData.hasData) {
        // Show no data message
        document.getElementById('noStockDataMessage')?.classList.remove('d-none');
        document.getElementById('stockTotalProducts').textContent = '0';
        document.getElementById('stockTotalValue').textContent = '€0,00';
        document.getElementById('stockSelectedDate').textContent = 'No Data';
        document.getElementById('stockDailyChanges').textContent = '0';
        return;
    }

    // Hide no data message
    document.getElementById('noStockDataMessage')?.classList.add('d-none');

    // Calculate daily changes count
    const changesCount = stockData.stockItems ? stockData.stockItems.filter(item => item.stockChange !== 0).length : 0;

    // Update summary cards
    document.getElementById('stockTotalProducts').textContent = formatNumber(stockData.totalProducts);
    document.getElementById('stockTotalValue').textContent = formatCurrency(stockData.totalStockValue);
    document.getElementById('stockDailyChanges').textContent = formatNumber(changesCount);

    const selectedDate = new Date(stockData.date);
    document.getElementById('stockSelectedDate').textContent = selectedDate.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });

    // Initialize filtered data and update table
    stockFilteredData = stockData.stockItems || [];
    stockCurrentPage = 1;
    updateStockTableDisplay();
}



function updateStockTableDisplay() {
    const tbody = document.getElementById('stockTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (stockFilteredData.length === 0) {
        document.getElementById('noStockDataMessage')?.classList.remove('d-none');
        document.getElementById('stockPaginationContainer')?.classList.add('d-none');
        return;
    }

    document.getElementById('noStockDataMessage')?.classList.add('d-none');
    document.getElementById('stockPaginationContainer')?.classList.remove('d-none');

    // Calculate pagination
    const totalItems = stockFilteredData.length;
    let startIndex, endIndex, displayData;

    if (stockItemsPerPage === 'all') {
        startIndex = 0;
        endIndex = totalItems;
        displayData = stockFilteredData;
    } else {
        startIndex = (stockCurrentPage - 1) * stockItemsPerPage;
        endIndex = Math.min(startIndex + stockItemsPerPage, totalItems);
        displayData = stockFilteredData.slice(startIndex, endIndex);
    }

    // Update table with paginated data
    displayData.forEach(item => {
        const row = document.createElement('tr');
        const stockValue = item.currentStock * item.price;

        // Determine change indicator
        let changeIndicator = '';
        let changeClass = '';
        if (item.changeType === 'increase') {
            changeIndicator = `<i class="fas fa-arrow-up text-success me-1"></i>+${formatNumber(Math.abs(item.stockChange))}`;
            changeClass = 'text-success';
        } else if (item.changeType === 'decrease') {
            changeIndicator = `<i class="fas fa-arrow-down text-danger me-1"></i>-${formatNumber(Math.abs(item.stockChange))}`;
            changeClass = 'text-danger';
        } else {
            changeIndicator = `<i class="fas fa-minus text-muted me-1"></i>0`;
            changeClass = 'text-muted';
        }

        // Determine stock level warning
        let stockBadgeClass = '';
        if (item.currentStock === 0) {
            stockBadgeClass = 'bg-danger';
        } else if (item.currentStock <= 5) {
            stockBadgeClass = 'bg-warning';
        } else {
            stockBadgeClass = 'bg-success';
        }

        // Add data attributes for filtering
        row.setAttribute('data-stock-level', item.currentStock);
        row.setAttribute('data-product-number', item.productNumber.toLowerCase());
        row.setAttribute('data-product-name', item.name.toLowerCase());

        row.innerHTML = `
            <td><span class="badge bg-light text-dark">${item.productNumber}</span></td>
            <td>
                <div class="text-truncate" style="max-width: 300px;" title="${item.name}">
                    ${item.name}
                </div>
            </td>
            <td class="text-center" data-sort-value="${item.currentStock}">
                <span class="badge ${stockBadgeClass}">
                    ${formatNumber(item.currentStock)}
                </span>
            </td>
            <td class="text-center" data-sort-value="${item.previousStock}">${formatNumber(item.previousStock)}</td>
            <td class="text-center ${changeClass}" data-sort-value="${item.stockChange}">${changeIndicator}</td>
            <td class="text-end" data-sort-value="${item.price}">${formatCurrency(item.price)}</td>
            <td class="text-end" data-sort-value="${item.cost}">${formatCurrency(item.cost)}</td>
            <td class="text-end" data-sort-value="${stockValue}">
                <span class="fw-bold">${formatCurrency(stockValue)}</span>
            </td>
        `;
        tbody.appendChild(row);
    });

    // Update pagination controls
    updateStockPaginationControls(totalItems, startIndex, endIndex);
}

function updateStockPaginationControls(totalItems, startIndex, endIndex) {
    const paginationInfo = document.getElementById('stockPaginationInfo');
    const prevPage = document.getElementById('stockPrevPage');
    const nextPage = document.getElementById('stockNextPage');
    const currentPageElement = document.getElementById('stockCurrentPage');

    if (stockItemsPerPage === 'all') {
        paginationInfo.textContent = `Showing all ${totalItems} items`;
        prevPage.classList.add('disabled');
        nextPage.classList.add('disabled');
        currentPageElement.querySelector('a').textContent = '1';
    } else {
        const totalPages = Math.ceil(totalItems / stockItemsPerPage);
        paginationInfo.textContent = `Showing ${startIndex + 1}-${endIndex} of ${totalItems}`;

        // Update previous button
        if (stockCurrentPage <= 1) {
            prevPage.classList.add('disabled');
        } else {
            prevPage.classList.remove('disabled');
        }

        // Update next button
        if (stockCurrentPage >= totalPages) {
            nextPage.classList.add('disabled');
        } else {
            nextPage.classList.remove('disabled');
        }

        // Update current page number
        currentPageElement.querySelector('a').textContent = stockCurrentPage;
    }
}

function filterStockTable(searchTerm) {
    applyStockFilters();
}

function filterStockByLevel(levelFilter) {
    applyStockFilters();
}

function applyStockFilters() {
    if (!stockData || !stockData.stockItems) return;

    const searchTerm = document.getElementById('searchStockTable').value.toLowerCase();
    const levelFilter = document.getElementById('stockLevelFilter').value;

    // Filter the original data
    stockFilteredData = stockData.stockItems.filter(item => {
        // Search filter
        const searchMatches = !searchTerm ||
            item.productNumber.toLowerCase().includes(searchTerm) ||
            item.name.toLowerCase().includes(searchTerm);

        // Level filter
        const levelMatches = checkStockLevelFilter(item, levelFilter);

        return searchMatches && levelMatches;
    });

    // Apply current sort
    applySortToFilteredData();

    // Reset to first page and update display
    stockCurrentPage = 1;
    updateStockTableDisplay();
}

function checkStockLevelFilter(item, levelFilter) {
    if (!levelFilter) return true;

    const stockLevel = item.currentStock;

    switch (levelFilter) {
        case 'critical':
            return stockLevel === 0;
        case 'low':
            return stockLevel >= 1 && stockLevel <= 5;
        case 'good':
            return stockLevel >= 6;
        default:
            return true;
    }
}

function sortStockTable(header) {
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);

    // Remove existing sort classes
    document.querySelectorAll('.sortable-stock').forEach(h => {
        h.classList.remove('sort-asc', 'sort-desc');
    });

    // Determine sort direction
    const isAscending = stockCurrentSort.column !== columnIndex || stockCurrentSort.direction === 'desc';
    const direction = isAscending ? 'asc' : 'desc';

    // Update sort state
    stockCurrentSort = { column: columnIndex, direction: direction };

    // Add sort class to header
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

    // Apply sort to filtered data
    applySortToFilteredData();

    // Reset to first page and update display
    stockCurrentPage = 1;
    updateStockTableDisplay();
}

function applySortToFilteredData() {
    console.log(stockFilteredData);
    if (!stockFilteredData || stockFilteredData.length === 0) return;

    const { column, direction } = stockCurrentSort;
    const isAscending = direction === 'asc';

    stockFilteredData.sort((a, b) => {
        let aVal, bVal;

        switch (column) {
            case 0: // Product Number
                aVal = a.productNumber;
                bVal = b.productNumber;
                break;
            case 1: // Description
                aVal = a.name;
                bVal = b.name;
                break;
            case 2: // Current Stock
                aVal = parseFloat(a.currentStock) || 0;
                bVal = parseFloat(b.currentStock) || 0;
                break;
            case 3: // Previous Stock
                aVal = parseFloat(a.previousStock) || 0;
                bVal = parseFloat(b.previousStock) || 0;
                break;
            case 4: // Daily Change
                aVal = parseFloat(a.stockChange) || 0;
                bVal = parseFloat(b.stockChange) || 0;
                break;
            case 5: // Unit Price
                aVal = parseFloat(a.price) || 0;
                bVal = parseFloat(b.price) || 0;
                break;
            case 6: // Unit Price
                aVal = parseFloat(a.cost) || 0;
                bVal = parseFloat(b.cost) || 0;
                break;
            case 7: // Stock Value
                aVal = parseFloat(a.currentStock * a.price) || 0;
                bVal = parseFloat(b.currentStock * b.price) || 0;
                break;
            default:
                aVal = a.productNumber;
                bVal = b.productNumber;
        }

        // Handle string vs numeric comparison
        if (typeof aVal === 'string' && typeof bVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }

        if (aVal < bVal) return isAscending ? -1 : 1;
        if (aVal > bVal) return isAscending ? 1 : -1;
        return 0;
    });
}

function exportStockToCSV() {
    // Use filtered data if available, otherwise use all stock data
    const dataToExport = stockFilteredData.length > 0 ? stockFilteredData : (stockData?.stockItems || []);

    if (dataToExport.length === 0) {
        alert('No stock data to export');
        return;
    }

    const headers = ['Product Number', 'Description', 'Current Stock', 'Previous Stock', 'Daily Change', 'Sales Price', 'Cost Price', 'Stock Value'];
    const csvContent = [
        headers.join(','),
        ...dataToExport.map(item => [
            `"${item.productNumber}"`,
            `"${item.name}"`,
            Math.round(item.currentStock),
            Math.round(item.previousStock),
            item.stockChange,
            parseFloat(item.price).toFixed(2),
            (item.currentStock * item.price).toFixed(2)
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    // Include filter info in filename if filters are applied
    let filename = `stock-data-${stockData.date}`;
    const searchTerm = document.getElementById('searchStockTable').value;
    const levelFilter = document.getElementById('stockLevelFilter').value;

    if (searchTerm || levelFilter) {
        filename += '-filtered';
    }

    a.download = `${filename}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function switchView() {
    if (currentView === 'daily') {
        // Show daily elements
        document.getElementById('dailyNavigationControls')?.classList.remove('d-none');
        document.getElementById('dailySummary')?.classList.remove('d-none');
        document.getElementById('salesDataTable')?.closest('.row')?.classList.remove('d-none');
        document.getElementById('topSoldProductsList')?.closest('.row')?.classList.remove('d-none');

        // Hide range elements
        document.getElementById('rangeSummaryControls')?.classList.add('d-none');
        document.getElementById('rangeSummary')?.classList.add('d-none');
        document.getElementById('rangeSummaryTable')?.classList.add('d-none');
        document.getElementById('rangeTopProducts')?.classList.add('d-none');

        updateDisplay();
    } else {
        // Hide daily elements
        document.getElementById('dailyNavigationControls')?.classList.add('d-none');
        document.getElementById('dailySummary')?.classList.add('d-none');
        document.getElementById('salesDataTable')?.closest('.row')?.classList.add('d-none');
        document.getElementById('topSoldProductsList')?.closest('.row')?.classList.add('d-none');

        // Show range elements
        document.getElementById('rangeSummaryControls')?.classList.remove('d-none');
        document.getElementById('rangeSummary')?.classList.remove('d-none');
        document.getElementById('rangeSummaryTable')?.classList.remove('d-none');
        document.getElementById('rangeTopProducts')?.classList.remove('d-none');

        updateRangeDisplay();
    }

    // Update charts for current view
    updateCharts();
}

function updateDisplay() {
    if (dailyData.length === 0) {
        // Hide daily navigation and show no data message
        const dailyNav = document.getElementById('currentDate')?.closest('.row');
        if (dailyNav) dailyNav.style.display = 'none';

        // Show no data message in summary cards
        updateSummaryCards({
            totalSold: 0,
            totalValue: 0,
            avgPrice: 0,
            uniqueProducts: 0
        });

        updateTable([]);
        updateTopProductsLists({
            topSoldProducts: [],
            topValueProducts: []
        });

        return;
    }

    // Show daily navigation
    const dailyNav = document.getElementById('currentDate')?.closest('.row');
    if (dailyNav) dailyNav.style.display = '';

    const currentDay = dailyData[currentDayIndex];

    // Update date display
    const dateElement = document.getElementById('currentDate');
    if (dateElement) {
        const date = new Date(currentDay.date);
        dateElement.textContent = date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // Update summary cards
    updateSummaryCards(currentDay);

    // Update table
    updateTable(currentDay.salesData);

    // Update top products lists
    updateTopProductsLists(currentDay);

    // Update charts
    updateCharts();

    // Update navigation buttons
    updateNavigationButtons();
}

function updateRangeDisplay() {
    if (!rangeAggregation || Object.keys(rangeAggregation).length === 0) {
        // Show no data message
        document.getElementById('rangeSummaryInfo').textContent = 'No aggregated data available';
        updateRangeSummaryCards({
            totalSold: 0,
            totalValue: 0,
            avgDailySales: 0,
            uniqueProducts: 0,
            totalDays: 0
        });
        updateRangeTable([]);
        updateRangeTopProductsLists({
            topSoldProducts: [],
            topValueProducts: []
        });
        return;
    }

    // Update range summary info
    const startDate = dailyData.length > 0 ? dailyData[0].date : '';
    const endDate = dailyData.length > 0 ? dailyData[dailyData.length - 1].date : '';
    document.getElementById('rangeSummaryInfo').textContent =
        `${startDate} to ${endDate} (${rangeAggregation.totalDays} days)`;

    // Update range summary cards
    updateRangeSummaryCards(rangeAggregation);

    // Update range table
    updateRangeTable(rangeAggregation.aggregatedSalesData || []);

    // Update range top products lists
    updateRangeTopProductsLists(rangeAggregation);
}

function updateRangeSummaryCards(rangeData) {
    document.getElementById('rangeTotalSoldValue').textContent = formatNumber(rangeData.totalSold);
    document.getElementById('rangeTotalValueValue').textContent = formatCurrency(rangeData.totalValue);
    document.getElementById('rangeAvgDailySalesValue').textContent = formatNumber(rangeData.avgDailySales);
    document.getElementById('rangeUniqueProductsValue').textContent = formatNumber(rangeData.uniqueProducts);

    // Update subtexts
    document.getElementById('rangeTotalSoldSubtext').textContent = `Across ${rangeData.totalDays} days`;
    document.getElementById('rangeTotalValueSubtext').textContent = 'Period total';
    document.getElementById('rangeAvgDailySalesSubtext').textContent = 'Units per day';
    document.getElementById('rangeUniqueProductsSubtext').textContent = 'In period';
}

function updateRangeTable(aggregatedData) {
    const tbody = document.getElementById('rangeSalesTableBody');
    const noDataMessage = document.getElementById('noRangeDataMessage');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (aggregatedData.length === 0) {
        noDataMessage?.classList.remove('d-none');
        return;
    }

    noDataMessage?.classList.add('d-none');

    aggregatedData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge bg-light text-dark">${item.productNumber}</span></td>
            <td>
                <div class="text-truncate" style="max-width: 300px;" title="${item.name}">
                    ${item.name}
                </div>
            </td>
            <td class="text-center">
                <span class="badge bg-primary">${formatNumber(item.totalSold)}</span>
            </td>
            <td class="text-end">
                <span class="fw-bold text-success">${formatCurrency(item.totalValue)}</span>
            </td>
            <td class="text-end">${formatCurrency(item.avgPrice)}</td>
            <td class="text-center">
                <span class="badge bg-info">${formatNumber(item.daysActive)}</span>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateRangeTopProductsLists(rangeData) {
    // Top sold products
    const topSoldContainer = document.getElementById('rangeTopSoldProductsList');
    if (topSoldContainer) {
        topSoldContainer.innerHTML = '';

        if (!rangeData.topSoldProducts || rangeData.topSoldProducts.length === 0) {
            topSoldContainer.innerHTML = '<p class="text-muted text-center">No data available</p>';
        } else {
            rangeData.topSoldProducts.slice(0, 10).forEach((product, index) => {
                const item = document.createElement('div');
                item.className = 'product-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge badge-rank bg-primary me-3">#${index + 1}</span>
                        <div>
                            <div class="fw-semibold">${product.productNumber}</div>
                            <small class="text-muted text-truncate" style="max-width: 200px; display: block;">
                                ${product.name}
                            </small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary fs-6">${formatNumber(product.totalSold)} units</span>
                        <br><small class="text-muted">${formatNumber(product.daysActive)} days active</small>
                    </div>
                `;
                topSoldContainer.appendChild(item);
            });
        }
    }

    // Top value products
    const topValueContainer = document.getElementById('rangeTopValueProductsList');
    if (topValueContainer) {
        topValueContainer.innerHTML = '';

        if (!rangeData.topValueProducts || rangeData.topValueProducts.length === 0) {
            topValueContainer.innerHTML = '<p class="text-muted text-center">No data available</p>';
        } else {
            rangeData.topValueProducts.slice(0, 10).forEach((product, index) => {
                const item = document.createElement('div');
                item.className = 'product-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge badge-rank bg-success me-3">#${index + 1}</span>
                        <div>
                            <div class="fw-semibold">${product.productNumber}</div>
                            <small class="text-muted text-truncate" style="max-width: 200px; display: block;">
                                ${product.name}
                            </small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success fs-6">${formatCurrency(product.totalValue)}</span>
                        <br><small class="text-muted">${formatNumber(product.daysActive)} days active</small>
                    </div>
                `;
                topValueContainer.appendChild(item);
            });
        }
    }
}

function updateSummaryCards(currentDay) {
    document.getElementById('totalSoldValue').textContent = formatNumber(currentDay.totalSold);
    document.getElementById('totalValueValue').textContent = formatCurrency(currentDay.totalValue);
    document.getElementById('avgPriceValue').textContent = formatCurrency(currentDay.avgPrice);
    document.getElementById('uniqueProductsValue').textContent = formatNumber(currentDay.uniqueProducts);
}

function updateTable(currentDaySalesData) {
    const tbody = document.getElementById('salesTableBody');
    const noDataMessage = document.getElementById('noDataMessage');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (currentDaySalesData.length === 0) {
        noDataMessage?.classList.remove('d-none');
        return;
    }

    noDataMessage?.classList.add('d-none');

    currentDaySalesData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge bg-light text-dark">${item.productNumber}</span></td>
            <td>
                <div class="text-truncate" style="max-width: 300px;" title="${item.name}">
                    ${item.name}
                </div>
            </td>
            <td class="text-center">${formatNumber(item.previousStock)}</td>
            <td class="text-center">${formatNumber(item.currentStock)}</td>
            <td class="text-center">${formatNumber(item.inbounds)}</td>
            <td class="text-center">${formatNumber(item.unitsSold)}</td>
            <td class="text-end">${formatCurrency(item.price)}</td>
            <td class="text-end">${formatCurrency(item.value)}</td>
            <td class="text-center">${formatPercentage(item.soldPercentage)}</td>
        `;
        tbody.appendChild(row);
    });
}

function updateTopProductsLists(currentDay) {
    const topSoldContainer = document.getElementById('topSoldProductsList');
    const topValueContainer = document.getElementById('topValueProductsList');

    if (topSoldContainer) {
        topSoldContainer.innerHTML = '';

        if (!currentDay.topSoldProducts || currentDay.topSoldProducts.length === 0) {
            topSoldContainer.innerHTML = '<p class="text-muted text-center">No data available</p>';
        } else {
            currentDay.topSoldProducts.slice(0, 10).forEach((product, index) => {
                const item = document.createElement('div');
                item.className = 'product-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge badge-rank bg-primary me-3">#${index + 1}</span>
                        <div>
                            <div class="fw-semibold">${product.productNumber}</div>
                            <small class="text-muted text-truncate" style="max-width: 200px; display: block;">
                                ${product.name}
                            </small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary fs-6">${formatNumber(product.unitsSold)} units</span>
                    </div>
                `;
                topSoldContainer.appendChild(item);
            });
        }
    }

    if (topValueContainer) {
        topValueContainer.innerHTML = '';

        if (!currentDay.topValueProducts || currentDay.topValueProducts.length === 0) {
            topValueContainer.innerHTML = '<p class="text-muted text-center">No data available</p>';
        } else {
            currentDay.topValueProducts.slice(0, 10).forEach((product, index) => {
                const item = document.createElement('div');
                item.className = 'product-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge badge-rank bg-success me-3">#${index + 1}</span>
                        <div>
                            <div class="fw-semibold">${product.productNumber}</div>
                            <small class="text-muted text-truncate" style="max-width: 200px; display: block;">
                                ${product.name}
                            </small>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-success fs-6">${formatCurrency(product.value)}</span>
                    </div>
                `;
                topValueContainer.appendChild(item);
            });
        }
    }
}



function updateNavigationButtons() {
    const prevDayButton = document.getElementById('prevDay');
    const nextDayButton = document.getElementById('nextDay');

    if (currentDayIndex === 0) {
        prevDayButton.disabled = true;
    } else {
        prevDayButton.disabled = false;
    }

    if (currentDayIndex === dailyData.length - 1) {
        nextDayButton.disabled = true;
    } else {
        nextDayButton.disabled = false;
    }
}



function updateSummaryCards(dayData) {
    document.getElementById('totalSoldValue').textContent = formatNumber(dayData.totalSold);
    document.getElementById('totalValueValue').textContent = formatCurrency(dayData.totalValue);
    document.getElementById('avgPriceValue').textContent = formatCurrency(dayData.avgPrice);
    document.getElementById('uniqueProductsValue').textContent = formatNumber(dayData.uniqueProducts);
}

function updateTable(salesData) {
    const tbody = document.getElementById('salesTableBody');
    const noDataMessage = document.getElementById('noDataMessage');

    if (!tbody) return;

    tbody.innerHTML = '';

    if (salesData.length === 0) {
        noDataMessage?.classList.remove('d-none');
        return;
    }

    noDataMessage?.classList.add('d-none');

    salesData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="badge bg-light text-dark">${item.productNumber}</span></td>
            <td>
                <div class="text-truncate" style="max-width: 300px;" title="${item.name}">
                    ${item.name}
                </div>
            </td>
            <td class="text-center">${formatNumber(item.previousStock)}</td>
            <td class="text-center">${formatNumber(item.currentStock)}</td>
            <td class="text-center">${formatNumber(item.inbounds)}</td>
            <td class="text-center">
                <span class="badge bg-primary">${formatNumber(item.sold)}</span>
            </td>
            <td class="text-end">${formatCurrency(item.price)}</td>
            <td class="text-end">
                <span class="fw-bold text-success">${formatCurrency(item.value)}</span>
            </td>
            <td class="text-center">
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" role="progressbar"
                         style="width: ${Math.min(item.percentSold, 100)}%"
                         aria-valuenow="${item.percentSold}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                        ${formatPercentage(item.percentSold)}
                    </div>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateTopProductsLists(dayData) {
    // Top sold products
    const topSoldContainer = document.getElementById('topSoldProductsList');
    if (topSoldContainer) {
        topSoldContainer.innerHTML = '';

        if (dayData.topSoldProducts.length === 0) {
            topSoldContainer.innerHTML = '<p class="text-muted text-center">No data available</p>';
        } else {
            dayData.topSoldProducts.slice(0, 5).forEach((product, index) => {
                const item = document.createElement('div');
                item.className = 'product-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge badge-rank bg-primary me-3">#${index + 1}</span>
                        <div>
                            <div class="fw-semibold">${product.productNumber}</div>
                            <small class="text-muted text-truncate" style="max-width: 200px; display: block;">
                                ${product.name}
                            </small>
                        </div>
                    </div>
                    <span class="badge bg-primary fs-6">${formatNumber(product.sold)} units</span>
                `;
                topSoldContainer.appendChild(item);
            });
        }
    }

    // Top value products
    const topValueContainer = document.getElementById('topValueProductsList');
    if (topValueContainer) {
        topValueContainer.innerHTML = '';

        if (dayData.topValueProducts.length === 0) {
            topValueContainer.innerHTML = '<p class="text-muted text-center">No data available</p>';
        } else {
            dayData.topValueProducts.slice(0, 5).forEach((product, index) => {
                const item = document.createElement('div');
                item.className = 'product-item d-flex justify-content-between align-items-center p-3 mb-2 border rounded';
                item.innerHTML = `
                    <div class="d-flex align-items-center">
                        <span class="badge badge-rank bg-success me-3">#${index + 1}</span>
                        <div>
                            <div class="fw-semibold">${product.productNumber}</div>
                            <small class="text-muted text-truncate" style="max-width: 200px; display: block;">
                                ${product.name}
                            </small>
                        </div>
                    </div>
                    <span class="badge bg-success fs-6">${formatCurrency(product.value)}</span>
                `;
                topValueContainer.appendChild(item);
            });
        }
    }
}

function updateCharts() {
    if (currentView === 'daily') {
        updateDailyCharts();
    } else {
        updateRangeCharts();
    }
}

function updateDailyCharts() {
    // Update sales overview chart with daily data
    const dates = dailyData.map(day => {
        const date = new Date(day.date);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });
    const unitsSold = dailyData.map(day => parseInt(day.totalSold) || 0);
    const salesValues = dailyData.map(day => parseFloat(day.totalValue) || 0);

    if (salesChart) {
        salesChart.updateOptions({
            xaxis: {
                categories: dates,
                title: {
                    text: 'Date'
                }
            },
            title: {
                text: 'Daily Sales Trend'
            }
        });
        salesChart.updateSeries([
            { name: 'Units Sold', data: unitsSold },
            { name: 'Sales Value (€)', data: salesValues }
        ]);
    }

    // Update top products pie chart with current day data
    const currentDay = dailyData[currentDayIndex];
    if (topProductsChart && currentDay && currentDay.topSoldProducts && currentDay.topSoldProducts.length > 0) {
        const labels = currentDay.topSoldProducts.slice(0, 5).map(p => p.productNumber);
        const series = currentDay.topSoldProducts.slice(0, 5).map(p => parseInt(p.sold) || 0);

        topProductsChart.updateOptions({
            labels: labels,
            title: {
                text: 'Top Products (Current Day)'
            }
        });
        topProductsChart.updateSeries(series);
    } else if (topProductsChart) {
        // Clear chart if no data
        topProductsChart.updateOptions({
            labels: [],
            title: {
                text: 'Top Products (Current Day)'
            }
        });
        topProductsChart.updateSeries([]);
    }
}

function updateRangeCharts() {
    // Update sales overview chart with range trend data
    if (rangeAggregation && rangeAggregation.dailyTrends) {
        const dates = rangeAggregation.dailyTrends.map(day => {
            const date = new Date(day.date);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        });
        const unitsSold = rangeAggregation.dailyTrends.map(day => parseInt(day.totalSold) || 0);
        const salesValues = rangeAggregation.dailyTrends.map(day => parseFloat(day.totalValue) || 0);

        if (salesChart) {
            salesChart.updateOptions({
                xaxis: {
                    categories: dates,
                    title: {
                        text: 'Date'
                    }
                },
                title: {
                    text: 'Range Sales Trend'
                }
            });
            salesChart.updateSeries([
                { name: 'Units Sold', data: unitsSold },
                { name: 'Sales Value (€)', data: salesValues }
            ]);
        }
    }

    // Update top products pie chart with range aggregated data
    if (topProductsChart && rangeAggregation && rangeAggregation.topSoldProducts && rangeAggregation.topSoldProducts.length > 0) {
        const labels = rangeAggregation.topSoldProducts.slice(0, 5).map(p => p.productNumber);
        const series = rangeAggregation.topSoldProducts.slice(0, 5).map(p => parseInt(p.totalSold) || 0);

        topProductsChart.updateOptions({
            labels: labels,
            title: {
                text: 'Top Products (Range Total)'
            }
        });
        topProductsChart.updateSeries(series);
    } else if (topProductsChart) {
        // Clear chart if no data
        topProductsChart.updateOptions({
            labels: [],
            title: {
                text: 'Top Products (Range Total)'
            }
        });
        topProductsChart.updateSeries([]);
    }
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevDay');
    const nextBtn = document.getElementById('nextDay');

    if (prevBtn) {
        prevBtn.disabled = currentDayIndex === 0;
        prevBtn.classList.toggle('disabled', currentDayIndex === 0);
    }

    if (nextBtn) {
        nextBtn.disabled = currentDayIndex === dailyData.length - 1;
        nextBtn.classList.toggle('disabled', currentDayIndex === dailyData.length - 1);
    }
}

function filterTable(searchTerm) {
    const tbody = document.getElementById('salesTableBody');
    if (!tbody) return;

    const rows = tbody.querySelectorAll('tr');
    const term = searchTerm.toLowerCase();

    rows.forEach(row => {
        const productNumber = row.cells[0].textContent.toLowerCase();
        const productName = row.cells[1].textContent.toLowerCase();
        const matches = productNumber.includes(term) || productName.includes(term);
        row.style.display = matches ? '' : 'none';
    });
}

function sortTable(header) {
    const table = document.getElementById('salesDataTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);

    // Remove existing sort classes
    document.querySelectorAll('.sortable').forEach(h => {
        h.classList.remove('sort-asc', 'sort-desc');
    });

    // Determine sort direction
    const isAscending = !header.classList.contains('sort-asc');
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

    // Sort rows
    rows.sort((a, b) => {
        let aVal = a.cells[columnIndex].textContent.trim();
        let bVal = b.cells[columnIndex].textContent.trim();

        // Handle numeric columns
        if (columnIndex >= 2 && columnIndex <= 8) {
            // Remove currency symbols, percentage signs, and German number formatting
            aVal = parseFloat(aVal.replace(/[€,%\s]/g, '').replace(/\./g, '').replace(/,/g, '.')) || 0;
            bVal = parseFloat(bVal.replace(/[€,%\s]/g, '').replace(/\./g, '').replace(/,/g, '.')) || 0;
        }

        if (aVal < bVal) return isAscending ? -1 : 1;
        if (aVal > bVal) return isAscending ? 1 : -1;
        return 0;
    });

    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));
}

function exportToCSV() {
    const currentDay = dailyData[currentDayIndex];
    if (!currentDay || currentDay.salesData.length === 0) {
        alert('No data to export');
        return;
    }

    const headers = ['Product Number', 'Description', 'Previous Stock', 'Current Stock', 'Inbounds', 'Units Sold', 'Price', 'Value', 'Sold %'];
    const csvContent = [
        headers.join(','),
        ...currentDay.salesData.map(item => [
            `"${item.productNumber}"`,
            `"${item.name}"`,
            Math.round(item.previousStock),
            Math.round(item.currentStock),
            Math.round(item.inbounds),
            Math.round(item.sold),
            parseFloat(item.price).toFixed(2),
            parseFloat(item.value).toFixed(2),
            parseFloat(item.percentSold).toFixed(1)
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-data-${currentDay.date}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function filterRangeTable(searchTerm) {
    const tbody = document.getElementById('rangeSalesTableBody');
    if (!tbody) return;

    const rows = tbody.querySelectorAll('tr');
    const term = searchTerm.toLowerCase();

    rows.forEach(row => {
        const productNumber = row.cells[0].textContent.toLowerCase();
        const productName = row.cells[1].textContent.toLowerCase();
        const matches = productNumber.includes(term) || productName.includes(term);
        row.style.display = matches ? '' : 'none';
    });
}

function sortRangeTable(header) {
    const table = document.getElementById('rangeSalesDataTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);

    // Remove existing sort classes
    document.querySelectorAll('.sortable').forEach(h => {
        h.classList.remove('sort-asc', 'sort-desc');
    });

    // Determine sort direction
    const isAscending = !header.classList.contains('sort-asc');
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

    // Sort rows
    rows.sort((a, b) => {
        let aVal = a.cells[columnIndex].textContent.trim();
        let bVal = b.cells[columnIndex].textContent.trim();

        // Handle numeric columns
        if (columnIndex >= 2 && columnIndex <= 5) {
            // Remove currency symbols and German number formatting
            aVal = parseFloat(aVal.replace(/[€,\s]/g, '').replace(/\./g, '').replace(/,/g, '.')) || 0;
            bVal = parseFloat(bVal.replace(/[€,\s]/g, '').replace(/\./g, '').replace(/,/g, '.')) || 0;
        }

        if (aVal < bVal) return isAscending ? -1 : 1;
        if (aVal > bVal) return isAscending ? 1 : -1;
        return 0;
    });

    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));
}

function exportRangeToCSV() {
    if (!rangeAggregation || !rangeAggregation.aggregatedSalesData || rangeAggregation.aggregatedSalesData.length === 0) {
        alert('No range data to export');
        return;
    }

    const startDate = dailyData.length > 0 ? dailyData[0].date : 'unknown';
    const endDate = dailyData.length > 0 ? dailyData[dailyData.length - 1].date : 'unknown';

    const headers = ['Product Number', 'Description', 'Total Units Sold', 'Total Sales Value', 'Average Price', 'Days Active'];
    const csvContent = [
        headers.join(','),
        ...rangeAggregation.aggregatedSalesData.map(item => [
            `"${item.productNumber}"`,
            `"${item.name}"`,
            Math.round(item.totalSold),
            parseFloat(item.totalValue).toFixed(2),
            parseFloat(item.avgPrice).toFixed(2),
            Math.round(item.daysActive)
        ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `range-sales-data-${startDate}-to-${endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Format currency helper
function formatCurrency(amount) {
    const numAmount = parseFloat(amount) || 0;
    return new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(numAmount);
}

// Format number helper (for whole numbers)
function formatNumber(number) {
    const numValue = parseInt(number) || 0;
    return new Intl.NumberFormat('de-DE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(numValue);
}

// Format percentage helper
function formatPercentage(percentage) {
    const numPercentage = parseFloat(percentage) || 0;
    return new Intl.NumberFormat('de-DE', {
        minimumFractionDigits: 1,
        maximumFractionDigits: 1
    }).format(numPercentage) + '%';
}

// Format decimal number helper (for cases where we need decimals but not currency)
function formatDecimal(number, decimals = 2) {
    const numValue = parseFloat(number) || 0;
    return new Intl.NumberFormat('de-DE', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(numValue);
}
</script>
{% endblock %}
