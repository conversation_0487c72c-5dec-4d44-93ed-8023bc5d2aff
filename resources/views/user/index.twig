{% include 'html.twig' %}

{% block body %}

<style>

@media(max-width: 752px) {
    .btn-outline-danger {
        margin-top: 10px;
    }
}

@media (max-width: 600px) {
    .card-header .row {
        display: flex;
        flex-direction: column !important;
        justify-content: center;
    }
    .card-header .card-title {
        text-align: center;
    }
    .card-header .btn-primary {
        margin-top: 10px;
    }
}

</style>

{% include 'component/sidebar.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row pt-4">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-header bg-body-tertiary">
                            <div class="row align-items-center">
                                <div class="col">                      
                                    <h4 class="card-title mb-0">Users</h4>                      
                                </div>
                                <div class="col-auto"> 
                                    {% if authUser.role == 'admin' %}                     
                                    <button class="btn btn-primary rounded-pill px-4" data-bs-toggle="modal" data-bs-target="#createUserModal">
                                        <i class="fas fa-plus me-2"></i> Add User
                                    </button>
                                    {% endif %}
                                </div>
                            </div>                                  
                        </div>
                        <div class="card-body p-4">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th class="border-top-0">Name</th>
                                            <th class="border-top-0">Email</th>
                                            <th class="border-top-0">Role</th>
                                            <th class="border-top-0">Created At</th>
                                            <th class="border-top-0 text-end">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for user in users %}
                                        <tr>
                                            <td>{{ user.name }}</td>
                                            <td>{{ user.email }}</td>
                                            <td>{{ user.role|capitalize }}</td>
                                            <td>{{ user.created_at|date('d M Y') }}</td>
                                            <td class="text-end">
                                                <a href="#" class="edit-user" data-id="{{ user.id }}" data-name="{{ user.name }}" data-email="{{ user.email }}" data-role="{{ user.role }}"><i class="fas fa-pen text-secondary fs-18"></i></a>
                                                <a href="#" class="delete-user" data-id="{{ user.id }}"><i class="fas fa-trash-alt text-secondary fs-18"></i></a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1" aria-labelledby="createUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createUserModalLabel">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Save User</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <input type="hidden" id="edit_user_id">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Password (leave empty to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="edit_role" class="form-label">Role</label>
                        <select class="form-select" id="edit_role" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateUserBtn">Update User</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // Save User
    $('#saveUserBtn').click(function() {
        const formData = $('#createUserForm').serialize();
        
        $.ajax({
            url: '{{ route("user.store") }}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val()
            },
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#createUserModal').modal('hide');
                    window.location.reload();
                }
            },
            error: function(xhr) {
                alert('Error creating user: ' + (xhr.responseJSON?.message || 'Unknown error'));
            }
        });
    });
    
    // Edit User
    $('.edit-user').click(function() {
        const userId = $(this).data('id');
        const userName = $(this).data('name');
        const userEmail = $(this).data('email');
        const userRole = $(this).data('role');
        
        $('#edit_user_id').val(userId);
        $('#edit_name').val(userName);
        $('#edit_email').val(userEmail);
        $('#edit_password').val(''); // Clear password field
        $('#edit_role').val(userRole);
        
        $('#editUserModal').modal('show');
    });
    
    // Update User
    $('#updateUserBtn').click(function() {
        const userId = $('#edit_user_id').val();
        const formData = $('#editUserForm').serialize();
        
        $.ajax({
            url: '/user/' + userId,
            method: 'PUT',
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val()
            },
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#editUserModal').modal('hide');
                    window.location.reload();
                }
            },
            error: function(xhr) {
                alert('Error updating user: ' + (xhr.responseJSON?.message || 'Unknown error'));
            }
        });
    });
    
    // Delete User
    $('.delete-user').click(function() {
        if (confirm('Are you sure you want to delete this user?')) {
            const userId = $(this).data('id');
            
            $.ajax({
                url: '/user/' + userId,
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        window.location.reload();
                    }
                },
                error: function(xhr) {
                    alert('Error deleting user: ' + (xhr.responseJSON?.message || 'Unknown error'));
                }
            });
        }
    });

    // Modal kapandığında formu temizle
    $('.modal').on('hidden.bs.modal', function () {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
        $(this).find('form').trigger('reset');
    });
});
</script>
{% endblock %}

