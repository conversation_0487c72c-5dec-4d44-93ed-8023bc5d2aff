{% extends 'html.twig' %}

{% block body %}
{% include 'component/sidebar.twig' %}

<div class="page-wrapper">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-sm-12">
                    <div class="page-title-box d-md-flex justify-content-md-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h4 class="page-title">Order Detail</h4>
                            <div id="oldInvoiceWarning" class="ms-3" style="display: none; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#oldInvoicesModal">
                                <div class="alert alert-danger mb-0 py-2">
                                    <i class="fas fa-exclamation-triangle me-1"></i> DİKKAT GEÇMİŞ ÖDENMEMİŞ FATURASI VAR
                                </div>
                            </div>
                        </div>
                        
                        <!-- Old Invoices Modal -->
                        <div class="modal fade" id="oldInvoicesModal" tabindex="-1" aria-hidden="true">
                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                <div class="modal-content">
                                    <div class="modal-header bg-danger text-white">
                                        <h5 class="modal-title">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Geçmiş Ödenmemiş Faturalar
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body" id="oldInvoicesContent">
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                let oldInvoicesData = null;
                                
                                $.ajax({
                                    url: '/old-invoices',
                                    method: 'GET',
                                    data: { invoice_number: '{{ invoice.invoice_number }}' },
                                    success: function(response) {
                                        if (response.success === true) {
                                            $('#oldInvoiceWarning').show();
                                            oldInvoicesData = response.invoices;
                                        }
                                    }
                                });
                                
                                $('#oldInvoicesModal').on('show.bs.modal', function() {
                                    if (!oldInvoicesData) return;
                                    
                                    let content = '<div class="list-group">';
                                    
                                    oldInvoicesData.forEach(invoice => {
                                        const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('de-DE');
                                        const dueDate = new Date(invoice.invoiceDate);
                                        dueDate.setDate(dueDate.getDate() + parseInt(invoice.timeToPay || 0));
                                        
                                        content += `
                                            <div class="list-group-item list-group-item-action">
                                                <div class="d-flex w-100 justify-content-between align-items-center">
                                                    <h5 class="mb-1">
                                                        <span class="badge bg-danger me-2">Ödenmemiş</span>
                                                        Fatura #${invoice.invoiceNumber}
                                                    </h5>
                                                    <span class="badge bg-primary rounded-pill">€${invoice.sumGross}</span>
                                                </div>
                                                <div class="row mt-2">
                                                    <div class="col-md-6">
                                                        <p class="mb-1">
                                                            <i class="fas fa-calendar-alt me-2 text-secondary"></i>
                                                            Fatura Tarihi: ${invoiceDate}
                                                        </p>
                                                        <p class="mb-1">
                                                            <i class="fas fa-hourglass-end me-2 text-secondary"></i>
                                                            Ödeme Süresi: ${invoice.timeToPay} gün
                                                        </p>
                                                        <p class="mb-0">
                                                            <i class="fas fa-calendar-times me-2 text-danger"></i>
                                                            Son Ödeme: ${dueDate.toLocaleDateString('de-DE')}
                                                        </p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1">
                                                            <i class="fas fa-user me-2 text-secondary"></i>
                                                            ${invoice.addressName}
                                                        </p>
                                                        <p class="mb-0 text-truncate">
                                                            <i class="fas fa-map-marker-alt me-2 text-secondary"></i>
                                                            ${invoice.addressStreet}, ${invoice.addressZip} ${invoice.addressCity}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                    });
                                    
                                    content += '</div>';
                                    $('#oldInvoicesContent').html(content);
                                });
                            });
                        </script> 
                        <div>
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="#">Nurederm</a></li>
                                <li class="breadcrumb-item"><a href="#">Ecommerce</a></li>
                                <li class="breadcrumb-item active">Detail</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Order #{{ invoice.invoice_number }}</h4>
                                    <p class="mb-0 text-muted mt-1">{{ invoice.invoice_date|date('d F Y') }} at {{ invoice.invoice_date|date('H:i') }}</p>
                                </div>
                                <div class="col-auto">
                                    {% if invoice.status == 0 %}
                                        <span class="badge bg-warning-subtle text-warning">Pending</span>
                                    {% elseif invoice.status == 1 %}
                                        <span class="badge bg-success-subtle text-success">Approved</span>
                                    {% elseif invoice.status == 99 %}
                                        <span class="badge bg-info-subtle text-danger">Test Order</span>
                                    {% else %}
                                        <span class="badge bg-danger-subtle text-danger">Cancelled</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div class="table-responsive">
                                <table class="table mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Item</th>
                                            <th class="text-end">Price</th>
                                            <th class="text-end">Quantity</th>
                                            <th class="text-end">Tax</th>
                                            <th class="text-end">Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in items %}
                                        <tr>
                                            <td>
                                                <p class="d-inline-block align-middle mb-0">
                                                    <span class="d-block align-middle mb-0 product-name text-body position-relative">
                                                        {{ item.product_name }}
                                                        {% if item.item_number is null %}
                                                            <i class="fas fa-exclamation-triangle text-warning" 
                                                               data-bs-toggle="tooltip" 
                                                               data-bs-placement="right" 
                                                               title="Product not found in inventory system" style="width: 24px;"></i>
                                                        {% else %}
                                                            <span class="stock-info" 
                                                                  data-item-number="{{ item.item_number }}"
                                                                  data-bs-toggle="tooltip" 
                                                                  data-bs-placement="right" 
                                                                  title="Loading stock info...">
                                                                <i class="fas fa-box text-primary" style="width: 24px;"></i>
                                                            </span>
                                                        {% endif %}
                                                    </span>
                                                    <span class="text-muted font-13">{{ item.description }}</span>
                                                </p>
                                            </td>
                                            <td class="text-end">€{{ item.price|number_format(2, '.', ',') }}</td>
                                            <td class="text-end">{{ item.quantity }}</td>
                                            <td class="text-end">{{ item.tax_rate }}%</td>
                                            <td class="text-end">€{{ item.gross_amount|number_format(2, '.', ',') }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Order Summary</h4>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div>
                                <div class="d-flex justify-content-between">
                                    <p class="text-body fw-semibold">Items subtotal:</p>
                                    <p class="text-body-emphasis fw-semibold">€{{ subtotal|number_format(2, '.', ',') }}</p>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <p class="text-body fw-semibold">Discount:</p>
                                    <p class="text-body fw-semibold">€{{ sumDiscountGross|number_format(2, '.', ',') }}</p>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <p class="text-body fw-semibold">Tax:</p>
                                    <p class="text-body fw-semibold">€{{ tax_total|number_format(2, '.', ',') }}</p>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <p class="text-body fw-semibold">Total:</p>
                                    <p class="text-success fw-semibold">€{{ invoice.total|number_format(2, '.', ',') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Customer Information</h4>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-profile-circle text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Name:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">{{ customer.first_name }} {{ customer.last_name }}</span>
                                </div>
                                {% if customer.company %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-building text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Company:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">{{ customer.company }}</span>
                                </div>
                                {% endif %}
                                {% if customer.address %}
                                <div class="d-flex align-items-start mb-2">
                                    <i class="iconoir-home text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Address:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto text-end">{{ customer.address }}</span>
                                </div>
                                {% endif %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-home text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">House Number:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">
                                        {% if customer.house_number %}
                                            {{ customer.house_number }}
                                        {% else %}
                                            <span data-bs-toggle="tooltip" data-bs-placement="top" title="Adres düzenlenmesi gerek">
                                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                            </span>
                                        {% endif %}
                                    </span>
                                </div>
                                {% if customer.city or customer.zip_code %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-city text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">City/Zip:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">
                                        {% if customer.city %}{{ customer.city }}{% endif %}
                                        {% if customer.zip_code %} - {{ customer.zip_code }}{% endif %}
                                    </span>
                                </div>
                                {% endif %}
                                {% if customer.country %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-globe text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Country:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">{{ customer.country }}</span>
                                </div>
                                {% endif %}
                                {% if customer.iban %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-bank text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">IBAN:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">{{ customer.iban }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    {% if deliveryData %}
                    {% set portica_data = deliveryData %}
                    {% if portica_data[0].deliveryAddress is defined %}
                    <div class="card mt-3">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Shipping Address</h4>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div class="mb-3">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-profile-circle text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Name:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">
                                        {{ portica_data[0].deliveryAddress.firstName }} {{ portica_data[0].deliveryAddress.lastName }}
                                    </span>
                                </div>
                                {% if portica_data[0].deliveryAddress.company %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-building text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Company:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">{{ portica_data[0].deliveryAddress.company }}</span>
                                </div>
                                {% endif %}
                                <div class="d-flex align-items-start mb-2">
                                    <i class="iconoir-home text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Address:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto text-end">
                                        {{ portica_data[0].deliveryAddress.address }} {{ portica_data[0].deliveryAddress.houseNumber }}
                                    </span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-city text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">City/Zip:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">
                                        {{ portica_data[0].deliveryAddress.city }} - {{ portica_data[0].deliveryAddress.zipCode }}
                                    </span>
                                </div>
                                <div class="d-flex align-items-center mb-2">
                                    <i class="iconoir-globe text-secondary fs-20 me-2"></i>
                                    <span class="text-body fw-semibold">Country:</span>
                                    <span class="text-body-emphasis fw-semibold ms-auto">{{ portica_data[0].deliveryAddress.county }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endif %}

                    {% if invoice.notes %}
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Invoice Notes</h4>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div class="mb-3">
                                <span class="text-end">{{ invoice.notes }}</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    {% if invoice.status == 1 and invoice.portica_response %}
                    <div class="card mt-3">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Portica API Response</h4>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div id="porticaResponseContainer" data-response="{{ invoice.portica_response|e('html_attr') }}">
                                <div class="alert alert-info mb-0">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-spinner fa-spin text-info fs-24 me-2"></i>
                                        <div>
                                            <h5 class="alert-heading mb-1">API Response</h5>
                                            <p class="mb-0">Loading response...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">Response received on: {{ invoice.approved_time|date('d M Y H:i:s') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Status -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Shipping Status</h4>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div id="shippingStatusContainer">
                                <div class="alert alert-info mb-0">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-spinner fa-spin text-info fs-24 me-2"></i>
                                        <div>
                                            <h5 class="alert-heading mb-1">Tracking Information</h5>
                                            <p class="mb-0">Loading shipping status...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script>
                    document.addEventListener('DOMContentLoaded', function() {
                        // Existing Portica response code...
                        const container = document.getElementById('porticaResponseContainer');
                        const responseData = container.getAttribute('data-response');
                        
                        try {
                            const parsedResponse = JSON.parse(responseData);
                            
                            if (parsedResponse.message) {
                                const isError = !parsedResponse.message.startsWith('Creation successful')
                                
                                const alertClass = isError ? 'alert-warning' : 'alert-success';
                                const iconClass = isError ? 'fas fa-exclamation-triangle text-warning' : 'fas fa-check-circle text-success';
                                
                                container.innerHTML = `
                                    <div class="alert ${alertClass} mb-0">
                                        <div class="d-flex align-items-center">
                                            <i class="${iconClass} fs-24 me-2"></i>
                                            <div>
                                                <h5 class="alert-heading mb-1">API Response</h5>
                                                <p class="mb-0">${parsedResponse.message}</p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                                // Error gelirse siparişi resetleme
                                if (isError) {
                                    const resetButton = document.createElement('button');
                                    resetButton.className = 'btn btn-danger mt-2';
                                    resetButton.innerHTML = '<i class="fas fa-times me-1"></i> Reset Order';
                                    resetButton.onclick = function() {
                                        $.ajax({
                                            url: '{{ route("order.status.reset", {"id": invoice.id}) }}',
                                            method: 'POST',
                                            data: { status: 0 },
                                            success: function(response) {
                                                if (response.success) {
                                                    window.location.reload();
                                                }
                                            },
                                            error: function(xhr) {
                                                alert('Error updating status: ' + xhr.responseJSON?.message || 'Unknown error');
                                            }
                                        });
                                    };
                                    container.appendChild(resetButton);
                                }

                            } else {
                                container.innerHTML = `
                                    <div class="alert alert-info mb-0">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-info-circle text-info fs-24 me-2"></i>
                                            <div>
                                                <h5 class="alert-heading mb-1">API Response</h5>
                                                <p class="mb-0">Raw response: ${responseData}</p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }
                        } catch (e) {
                            container.innerHTML = `
                                <div class="alert alert-info mb-0">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-info-circle text-info fs-24 me-2"></i>
                                        <div>
                                            <h5 class="alert-heading mb-1">API Response</h5>
                                            <p class="mb-0">Raw response: ${responseData}</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                        
                        // Fetch shipping status
                        const shippingContainer = document.getElementById('shippingStatusContainer');
                        
                        fetch('/api/order-status/{{ invoice.invoice_number }}', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.data && data.data.packages && data.data.packages.length > 0) {
                                // Order state mapping
                                const orderStateMap = {
                                    1: { label: 'New', class: 'bg-info-subtle text-info' },
                                    2: { label: 'In Progress', class: 'bg-warning-subtle text-warning' },
                                    3: { label: 'Shipped', class: 'bg-success-subtle text-success' },
                                    4: { label: 'Completed', class: 'bg-success-subtle text-success' },
                                    5: { label: 'Cancelled', class: 'bg-danger-subtle text-danger' }
                                };
                                
                                const orderState = data.data.orderState;
                                const stateInfo = orderStateMap[orderState] || { label: 'Unknown', class: 'bg-secondary-subtle text-secondary' };
                                
                                let packagesHtml = '';
                                data.data.packages.forEach((pkg, index) => {
                                    const shippingDate = new Date(pkg.shippingDate).toLocaleString('de-DE');
                                    packagesHtml += `
                                        <div class="card mb-2 border">
                                            <div class="card-body p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <span class="badge ${stateInfo.class} me-2">Package ${index + 1}</span>
                                                        <span class="text-muted">${pkg.carrier}</span>
                                                    </div>
                                                    <div>
                                                        <span class="text-muted">Shipped: ${shippingDate}</span>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div class="text-truncate">
                                                        <i class="fas fa-barcode text-secondary me-1"></i>
                                                        <span class="fw-medium">${pkg.packageNumber}</span>
                                                    </div>
                                                    <a href="${pkg.trackingLink}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-external-link-alt me-1"></i> Track
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                                });
                                
                                shippingContainer.innerHTML = `
                                    <div class="mb-3">
                                        <div class="d-flex align-items-center mb-3">
                                            <span class="badge ${stateInfo.class} me-2">
                                                <i class="fas fa-truck me-1"></i> ${stateInfo.label}
                                            </span>
                                            <span class="text-muted">Order #${data.data.orderNumber}</span>
                                        </div>
                                        ${packagesHtml}
                                    </div>
                                `;
                            } else if (data.success && data.data) {
                                // Handle case where there's data but no packages
                                shippingContainer.innerHTML = `
                                    <div class="alert alert-info mb-0">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-info-circle text-info fs-24 me-2"></i>
                                            <div>
                                                <h5 class="alert-heading mb-1">Shipping Status</h5>
                                                <p class="mb-0">No shipping information available yet.</p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            } else {
                                // Handle error or no data
                                shippingContainer.innerHTML = `
                                    <div class="alert alert-warning mb-0">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-exclamation-triangle text-warning fs-24 me-2"></i>
                                            <div>
                                                <h5 class="alert-heading mb-1">Shipping Status</h5>
                                                <p class="mb-0">Could not retrieve shipping information.</p>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching shipping status:', error);
                            // Format raw JSON if parsing fails
                            shippingContainer.innerHTML = `
                                <div class="alert alert-danger mb-0">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-exclamation-circle text-danger fs-24 me-2"></i>
                                        <div>
                                            <h5 class="alert-heading mb-1">Error</h5>
                                            <p class="mb-0">Failed to load shipping information.</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                    });
                    </script>
                    {% endif %}

                    <!-- Invoice PDF Preview -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h4 class="card-title">Invoice PDF</h4>
                                </div>
                                <div class="col-auto">
                                    <a href="/invoices/{{ invoice.invoice_number }}.pdf" target="_blank" class="btn btn-sm btn-primary">
                                        <i class="fas fa-download me-1"></i> Download
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body pt-0">
                            <div class="embed-responsive">
                                <iframe src="/invoices/{{ invoice.invoice_number }}.pdf" width="100%" height="400" class="border-0"></iframe>
                            </div>
                        </div>
                    </div>

                    <div class="col-auto">
                    {% set hasShipment = invoice.shipmentPackages is not empty %}
                        {% if invoice.status == 0 %}
                            <button class="btn btn-success update-status w-100 mb-2" data-status="1" id="approveOrderBtn">
                                <i class="fas fa-check me-1"></i> Approve Order
                            </button>
                            <button class="btn btn-danger update-status w-100" data-status="2">
                                <i class="fas fa-times me-1"></i> Cancel Order
                            </button>
                        <button class="btn btn-primary w-100 mt-2" id="editOrderBtn">
                            <i class="fas fa-pen me-1"></i> Edit Order
                        </button>
                        {% else %}
                        <button class="btn btn-primary w-100 mt-2" disabled>
                            Order already approved
                        </button>
                        <button id="cancelOrderBtn" 
                            class="btn btn-primary w-100 mt-2
                                {% if hasShipment %} disabled-button {% endif %}"
                            {% if hasShipment %}
                                style="pointer-events: none; opacity: 0.6; cursor: not-allowed;"
                                title="Shipped orders can not be cancelled."
                                disabled
                            {% endif %}
                        >
                            <i class="fas fa-print me-1"></i> Cancel Order
                        </button>
                        {% endif %}

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if invoice.status == 0 %}
<script>
$(document).ready(function() {
    // CSRF Token ayarı
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Cancel Order işlemi
    $('.update-status[data-status="2"]').click(function() {
        if (!confirm('Are you sure you want to cancel this order?')) {
            return;
        }
        
        $.ajax({
            url: '{{ route("order.status.update", {"id": invoice.id}) }}',
            method: 'POST',
            data: { status: 2 },
            success: function(response) {
                if (response.success) {
                    console.log(response);
                    //window.location.reload();
                }
            },
            error: function(xhr) {
                alert('Error updating status: ' + xhr.responseJSON?.message || 'Unknown error');
            }
        });
    });

    // Approve Order ve Edit Order butonları için ayrı handler'lar
    $('#approveOrderBtn').click(function(e) {
        e.preventDefault();
        const modal = new bootstrap.Modal(document.getElementById('editOrderModal'));
        $('#saveAndApprove').text('Save and Approve').data('mode', 'approve');
        modal.show();
    });

    $('#editOrderBtn').click(function(e) {
        e.preventDefault();
        const modal = new bootstrap.Modal(document.getElementById('editOrderModal'));
        $('#saveAndApprove').text('Save Changes').data('mode', 'edit');

        // Load alternative address if exists
        loadCustomerAlternativeAddress();

        modal.show();
    });


    // Save butonu için tek bir handler
    $('#saveAndApprove').off('click').on('click', function(e) {
        e.preventDefault();
        
        const $button = $(this);
        const originalText = $button.text();
        const mode = $button.data('mode');
        
        // Butonu disable et ve loading göster
        $button.prop('disabled', true).text('Saving...');
        
        const formData = new FormData($('#editOrderForm')[0]);
        
        // Mode'a göre endpoint ve data ayarla
        let url;
        if (mode === 'edit') {
            url = '{{ route("order.update.details", {"id": invoice.id}) }}';
        } else {
            url = '{{ route("order.approve", {"id": invoice.id}) }}';
            formData.append('status', 1);
        }

        if ($('#useAlternativeAddress').is(':checked')) {
            formData.append('use_alternative_address', true);
        }

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert('Error updating order: ' + (response.message || 'Unknown error'));
                    $button.prop('disabled', false).text(originalText);
                }
            },
            error: function(xhr) {
                alert('Error updating order: ' + xhr.responseJSON?.message || 'Unknown error');
                $button.prop('disabled', false).text(originalText);
            }
        });
    });

    // Alternatif adres checkbox kontrolü
    $('#useAlternativeAddress').change(function() {
        if (this.checked) {
            $('#defaultAddressDiv').hide();
            $('#alternativeAddressDiv').show();
        } else {
            $('#defaultAddressDiv').show();
            $('#alternativeAddressDiv').hide();
        }
    });

    // Yeni item ekleme
    let itemIndex = {{ items|length }};
    $('#addNewItem').click(function() {
        const template = document.querySelector('#itemTemplate').content.cloneNode(true);
        const div = template.querySelector('.order-item');
        
        div.innerHTML = div.innerHTML.replace(/INDEX/g, itemIndex++);
        
        document.querySelector('#orderItems').appendChild(div);
        calculateTotals();
    });

    // Item silme
    $(document).on('click', '.remove-item', function() {
        $(this).closest('.order-item').remove();
        calculateTotals();
    });

    // Toplam hesaplama
    $(document).on('input', '.item-price, .item-quantity, .item-tax, [name="discount"]', calculateTotals);
});
</script>
{% endif %}

<!-- Edit Order Modal -->
<div class="modal fade" id="editOrderModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Order #{{ invoice.invoice_number }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editOrderForm">
                    <!-- Customer Information -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Customer Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Customer Name</label>
                                        <input type="text" class="form-control" value="{{ customer.first_name }} {{ customer.last_name }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">IBAN</label>
                                        <input type="text" class="form-control" name="iban" value="{{ customer.iban }}">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="useAlternativeAddress">
                                    <label class="form-check-label" for="useAlternativeAddress">
                                        Ship to different address
                                    </label>
                                </div>
                                <div id="defaultAddressDiv">
                                    <label class="form-label">Current Address</label>
                                    <textarea class="form-control" readonly>{{ customer.address }} {{ customer.house_number }} {{ customer.zip_code }} {{ customer.city }} {{ customer.country }}</textarea>
                                </div>
                                <div id="alternativeAddressDiv" style="display: none;">
                                    <h6 class="mb-3">Alternative Shipping Address</h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">First Name*</label>
                                            <input type="text" class="form-control" name="delivery_firstName" required>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">Last Name*</label>
                                            <input type="text" class="form-control" name="delivery_lastName" required>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <label class="form-label">Company</label>
                                        <input type="text" class="form-control" name="delivery_company">
                                    </div>
                                    <div class="row">
                                        <div class="col-md-8 mb-2">
                                            <label class="form-label">Address*</label>
                                            <input type="text" class="form-control" name="delivery_address" required>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <label class="form-label">House Number*</label>
                                            <input type="text" class="form-control" name="delivery_housenumber" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <label class="form-label">Zip Code*</label>
                                            <input type="text" class="form-control" name="delivery_zipcode" required>
                                        </div>
                                        <div class="col-md-8 mb-2">
                                            <label class="form-label">City*</label>
                                            <input type="text" class="form-control" name="delivery_city" required>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">Country*</label>
                                            <input type="text" class="form-control" name="delivery_country" required>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <label class="form-label">Phone*</label>
                                            <input type="text" class="form-control" name="delivery_phone" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Order Items</h5>
                            <button type="button" class="btn btn-primary btn-sm" id="addNewItem">
                                <i class="fas fa-plus me-1"></i> Add Item
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="orderItems">
                                {% for item in items %}
                                <div class="order-item mb-3 border-bottom pb-3">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="mb-2">
                                                <label class="form-label">Product Name</label>
                                                <input type="text" class="form-control" name="items[{{ loop.index0 }}][product_name]" value="{{ item.product_name }}" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="mb-2">
                                                <label class="form-label">Item Identifier</label>
                                                <input type="text" class="form-control" name="items[{{ loop.index0 }}][item_number]" value="{{ item.item_number }}" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="mb-2">
                                                <label class="form-label">Price</label>
                                                <input type="number" step="0.01" class="form-control item-price" name="items[{{ loop.index0 }}][price]" value="{{ item.price }}">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="mb-2">
                                                <label class="form-label">Quantity</label>
                                                <input type="number" class="form-control item-quantity" name="items[{{ loop.index0 }}][quantity]" value="{{ item.quantity }}">
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="mb-2">
                                                <label class="form-label">Tax Rate (%)</label>
                                                <input type="number" step="0.01" class="form-control item-tax" name="items[{{ loop.index0 }}][tax_rate]" value="{{ item.tax_rate }}">
                                            </div>
                                        </div>
                                        <div class="col-md-1">
                                            <div class="mb-2">
                                                <label class="form-label">Actions</label>
                                                <button type="button" class="btn btn-danger btn-sm w-100 remove-item">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Order Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Discount Amount</label>
                                        <input type="number" step="0.01" class="form-control" name="discount" value="{{ sumDiscountGross }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Notes</label>
                                        <textarea class="form-control" name="notes">{{ invoice.notes }}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Subtotal:</td>
                                            <td class="text-end" id="summarySubtotal">€{{ subtotal|number_format(2, '.', ',') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tax Total:</td>
                                            <td class="text-end" id="summaryTax">€{{ tax_total|number_format(2, '.', ',') }}</td>
                                        </tr>
                                        <tr>
                                            <td>Discount:</td>
                                            <td class="text-end" id="summaryDiscount">€{{ sumDiscountGross|number_format(2, '.', ',') }}</td>
                                        </tr>
                                        <tr class="table-primary">
                                            <td><strong>Total:</strong></td>
                                            <td class="text-end"><strong id="summaryTotal">€{{ invoice.total|number_format(2, '.', ',') }}</strong></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="saveAndApprove">
                    <i class="fas fa-check me-1"></i> Save and Approve
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Item Template -->
<template id="itemTemplate">
    <div class="order-item mb-3 border-bottom pb-3">
        <div class="row">
            <div class="col-md-3">
                <div class="mb-2">
                    <label class="form-label">Product Name</label>
                    <input type="text" class="form-control" name="items[INDEX][product_name]">
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-2">
                    <label class="form-label">Item Identifier</label>
                    <input type="text" class="form-control" name="items[INDEX][item_number]">
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-2">
                    <label class="form-label">Price</label>
                    <input type="number" step="0.01" class="form-control item-price" name="items[INDEX][price]">
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-2">
                    <label class="form-label">Quantity</label>
                    <input type="number" class="form-control item-quantity" name="items[INDEX][quantity]">
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-2">
                    <label class="form-label">Tax Rate (%)</label>
                    <input type="number" step="0.01" class="form-control item-tax" name="items[INDEX][tax_rate]">
                </div>
            </div>
            <div class="col-md-1">
                <div class="mb-2">
                    <label class="form-label">Actions</label>
                    <button type="button" class="btn btn-danger remove-item">
                        <i class="fas fa-trash"></i> Remove
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
$(document).ready(function() {
    // CSRF Token ayarı
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Approve Order butonu için handler
    $('#approveOrderBtn').click(function(e) {
        e.preventDefault();
        
        // Check if house number exists
        const houseNumberExists = {{ customer.house_number ? 'true' : 'false' }};
        
        if (!houseNumberExists) {
            // Show warning modal
            const warningModal = new bootstrap.Modal(document.getElementById('houseNumberWarningModal'));
            warningModal.show();
            return;
        }
        
        // If house number exists, proceed with normal flow
        const modal = new bootstrap.Modal(document.getElementById('editOrderModal'));
        $('#saveAndApprove').text('Save and Approve').data('mode', 'approve');

        // Load alternative address if exists
        loadCustomerAlternativeAddress();

        modal.show();
    });
    
    // Edit Customer button in warning modal
    $('#houseNumberWarningModal #editCustomerBtn').click(function() {
        window.location.href = '{{ route("customer.edit", {"id": customer.id}) }}';
    });

    // Edit Order butonu için handler
    $('#editOrderBtn').click(function(e) {
        e.preventDefault();
        const modal = new bootstrap.Modal(document.getElementById('editOrderModal'));
        $('#saveAndApprove').text('Save Changes').data('mode', 'edit');

        // Load alternative address if exists
        loadCustomerAlternativeAddress();

        modal.show();
    });

    // Save butonu için handler
    $('#saveAndApprove').click(function() {
        // Existing code...
    });
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Function to load customer alternative address
    function loadCustomerAlternativeAddress() {
        const customerId = {{ customer.id }};

        $.ajax({
            url: `/customer/${customerId}/alternative-address`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    // Check the "Ship to different address" checkbox
                    $('#useAlternativeAddress').prop('checked', true);

                    // Show alternative address div and hide default address div
                    $('#defaultAddressDiv').hide();
                    $('#alternativeAddressDiv').show();

                    // Pre-fill the alternative address fields
                    const data = response.data;
                    $('input[name="delivery_firstName"]').val(data.first_name || '');
                    $('input[name="delivery_lastName"]').val(data.last_name || '');
                    $('input[name="delivery_company"]').val(data.company || '');
                    $('input[name="delivery_address"]').val(data.address || '');
                    $('input[name="delivery_housenumber"]').val(data.house_number || '');
                    $('input[name="delivery_zipcode"]').val(data.zip_code || '');
                    $('input[name="delivery_city"]').val(data.city || '');
                    $('input[name="delivery_country"]').val(data.country || '');
                    $('input[name="delivery_phone"]').val(data.phone || '');
                }
            },
            error: function(xhr) {
                console.log('No alternative address found or error occurred:', xhr.responseJSON?.message);
                // Don't show error to user as this is expected when no alternative address exists
            }
        });
    }
});
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // CSRF Token ayarı
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Approve Order butonu için handler
    $('#approveOrderBtn').click(function(e) {
        e.preventDefault();
        const modal = new bootstrap.Modal(document.getElementById('editOrderModal'));
        // Load alternative address if exists
        loadCustomerAlternativeAddress();

        modal.show();
    });

    // Edit Order butonu için handler
    $('#editOrderBtn').click(function(e) {
        e.preventDefault();
        const modal = new bootstrap.Modal(document.getElementById('editOrderModal'));

        // Load alternative address if exists
        loadCustomerAlternativeAddress();

        modal.show();
    });
    $('#cancelOrderBtn').off('click').on('click', function(e) {
            e.preventDefault();
            if (!confirm('Are you sure you want to cancel this order?')) {
                return;
            }
            $.ajax({
                url: '{{ route("order.status.cancel", {"id": invoice.id}) }}',
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        window.location.reload();
                    } else {
                        alert('Error updating order status: ' + (response.message || 'Unknown error'));
                    }
                },
                error: function(xhr) {
                    alert('Error updating order status: ' + xhr.responseJSON?.message || 'Unknown error');
                }
            });
    });
    
    // Save butonu için handler (status değiştirmeden kaydetme)
    $('#saveAndApprove').text('Save Changes'); // Buton metnini değiştir

    $('#saveAndApprove').click(function() {
        const formData = new FormData($('#editOrderForm')[0]);
        
        if ($('#useAlternativeAddress').is(':checked')) {
            formData.append('use_alternative_address', true);
        }

        $.ajax({
            url: '{{ route("order.update", {"id": invoice.id}) }}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    window.location.reload();
                }
            },
            error: function(xhr) {
                alert('Error updating order: ' + xhr.responseJSON?.message || 'Unknown error');
            }
        });
    });

    // Function to load customer alternative address
    function loadCustomerAlternativeAddress() {
        const customerId = {{ customer.id }};

        $.ajax({
            url: `/customer/${customerId}/alternative-address`,
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    // Check the "Ship to different address" checkbox
                    $('#useAlternativeAddress').prop('checked', true);

                    // Show alternative address div and hide default address div
                    $('#defaultAddressDiv').hide();
                    $('#alternativeAddressDiv').show();

                    // Pre-fill the alternative address fields
                    const data = response.data;
                    $('input[name="delivery_firstName"]').val(data.first_name || '');
                    $('input[name="delivery_lastName"]').val(data.last_name || '');
                    $('input[name="delivery_company"]').val(data.company || '');
                    $('input[name="delivery_address"]').val(data.address || '');
                    $('input[name="delivery_housenumber"]').val(data.house_number || '');
                    $('input[name="delivery_zipcode"]').val(data.zip_code || '');
                    $('input[name="delivery_city"]').val(data.city || '');
                    $('input[name="delivery_country"]').val(data.country || '');
                    $('input[name="delivery_phone"]').val(data.phone || '');
                }
            },
            error: function(xhr) {
                console.log('No alternative address found or error occurred:', xhr.responseJSON?.message);
                // Don't show error to user as this is expected when no alternative address exists
            }
        });
    }
});
</script>
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // Fetch stock info for all products
    $('.stock-info').each(function() {
        const $stockInfo = $(this);
        const itemNumber = $stockInfo.data('item-number');

        fetch('/api/stock-proxy', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(response => {
            if (response.data) {
                const stockItem = response.data.find(item => item.productNumber === itemNumber);
                if (stockItem) {
                    const stock = stockItem.stock;
                    let stockClass = 'text-success';
                    if (stock <= 5) stockClass = 'text-danger';
                    else if (stock <= 10) stockClass = 'text-warning';

                    $stockInfo.find('i')
                        .removeClass('text-primary')
                        .addClass(stockClass);
                    
                    $stockInfo.attr('title', stock === 0 
                        ? 'Out of Stock!' 
                        : `Stock Available: ${stock} units`)
                        .tooltip('dispose')
                        .tooltip();

                    if (stock === 0) {
                        $stockInfo.find('i')
                            .removeClass('text-primary text-success text-danger')
                            .addClass('text-warning')
                            .removeClass('fa-box')
                            .addClass('fa-exclamation-circle');
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error fetching stock data:', error);
            $stockInfo.attr('title', 'Error loading stock information')
                .tooltip('dispose')
                .tooltip();
        });
    });
});
</script>
<!-- House Number Warning Modal -->
<div class="modal fade" id="houseNumberWarningModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog" style="max-width: 500px;margin-top: 20vh;">
        <div class="modal-content" style="border: 2px black solid;">
            <div class="modal-header" style="background-color: #ad1818!important;">
                <h5 class="modal-title">Missing House Number ⚠️</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>You cannot submit this order without a House Number value.</p>
                <p>Would you like to edit the customer information?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                <button type="button" class="btn btn-primary" id="editCustomerBtn">Yes</button>
            </div>
        </div>
    </div>
</div>
<script>
$(document).ready(function() {
    // Modal tamamen kapandığında backdrop'u temizle
    // if modal name is not 'editOrderModal'
    if ($('.modal').attr('id') == 'editOrderModal') {
    $('.modal').on('hidden.bs.modal', function () {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
    });
    }
});
</script>
<script>
$(document).ready(function() {
    // Remove any existing event handlers to prevent duplicates
    $(document).off('keyup', 'input[name$="[item_number]"]');
    $(document).off('keyup', 'input[name$="[product_name]"]');
    
    // Remove any existing dropdowns when clicking anywhere in the document
    $(document).on('click', function(e) {
        if (!$(e.target).closest('input[name$="[item_number]"], input[name$="[product_name]"]').length) {
            $('.autocomplete-dropdown').remove();
        }
    });
    
    // Direct autocomplete for item number fields
    $(document).on('keyup', 'input[name$="[item_number]"]', function() {
        const $input = $(this);
        const term = $input.val().trim();
        
        // Only search if at least 2 characters
        if (term.length < 2) {
            $('.autocomplete-dropdown').remove();
            return;
        }
        
        // Get the row index from the input name
        const index = $input.attr('name').match(/\[(\d+)\]/)[1];
        const $productNameField = $(`input[name="items[${index}][product_name]"]`);
        const $priceField = $(`input[name="items[${index}][price]"]`);
        
        // Make AJAX request
        $.ajax({
            url: '{{ route("product.search") }}',
            method: 'GET',
            data: {
                term: term,
                searchType: 'item'
            },
            success: function(data) {
                // Clear any existing dropdown
                $('.autocomplete-dropdown').remove();
                
                if (data.length === 0) {
                    return;
                }
                
                // Create dropdown
                const $dropdown = $('<div class="autocomplete-dropdown"></div>');
                
                // Position dropdown correctly
                const inputPos = $input.offset();
                $dropdown.css({
                    position: 'absolute',
                    top: inputPos.top + $input.outerHeight(),
                    left: inputPos.left,
                    width: $input.outerWidth(),
                    maxHeight: '200px',
                    overflowY: 'auto',
                    backgroundColor: 'white',
                    border: '1px solid #ccc',
                    borderRadius: '4px',
                    zIndex: 9999,
                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                });
                
                // Add items to dropdown
                data.forEach(function(item) {
                    const $item = $('<div class="autocomplete-item"></div>');
                    $item.text(item.itemNumber + ' - ' + item.description);
                    $item.css({
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderBottom: '1px solid #eee'
                    });
                    
                    // Hover effect
                    $item.hover(
                        function() { $(this).css('backgroundColor', '#f0f0f0'); },
                        function() { $(this).css('backgroundColor', 'white'); }
                    );
                    
                    // Click handler
                    $item.on('click', function(e) {
                        e.stopPropagation(); // Prevent event bubbling
                        $input.val(item.itemNumber).trigger('input').trigger('change');
                        $productNameField.val(item.description).trigger('input').trigger('change');
                        $priceField.val(item.priceGross).trigger('input').trigger('change');
                        calculateTotals();
                        $('.autocomplete-dropdown').remove();
                    });
                    
                    $dropdown.append($item);
                });
                
                // Add dropdown to body
                $('body').append($dropdown);
            },
            error: function(xhr) {
                console.error('Error searching products:', xhr);
            }
        });
    });
    
    // Similar implementation for product name fields
    $(document).on('keyup', 'input[name$="[product_name]"]', function() {
        const $input = $(this);
        const term = $input.val().trim();
        
        if (term.length < 2) {
            $('.autocomplete-dropdown').remove();
            return;
        }
        
        const index = $input.attr('name').match(/\[(\d+)\]/)[1];
        const $itemNumberField = $(`input[name="items[${index}][item_number]"]`);
        const $priceField = $(`input[name="items[${index}][price]"]`);
        
        $.ajax({
            url: '{{ route("product.search") }}',
            method: 'GET',
            data: {
                term: term,
                searchType: 'name'
            },
            success: function(data) {
                $('.autocomplete-dropdown').remove();
                
                if (data.length === 0) {
                    return;
                }
                
                const $dropdown = $('<div class="autocomplete-dropdown"></div>');
                
                // Position dropdown correctly
                const inputPos = $input.offset();
                $dropdown.css({
                    position: 'absolute',
                    top: inputPos.top + $input.outerHeight(),
                    left: inputPos.left,
                    width: $input.outerWidth(),
                    maxHeight: '200px',
                    overflowY: 'auto',
                    backgroundColor: 'white',
                    border: '1px solid #ccc',
                    borderRadius: '4px',
                    zIndex: 9999,
                    boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                });
                
                data.forEach(function(item) {
                    const $item = $('<div class="autocomplete-item"></div>');
                    $item.text(item.description);
                    $item.css({
                        padding: '8px 12px',
                        cursor: 'pointer',
                        borderBottom: '1px solid #eee'
                    });
                    
                    $item.hover(
                        function() { $(this).css('backgroundColor', '#f0f0f0'); },
                        function() { $(this).css('backgroundColor', 'white'); }
                    );
                    
                    $item.on('click', function(e) {
                        e.stopPropagation(); // Prevent event bubbling
                        $input.val(item.description);
                        $itemNumberField.val(item.itemNumber);
                        $priceField.val(item.priceGross);
                        calculateTotals();
                        $('.autocomplete-dropdown').remove();
                    });
                    
                    $dropdown.append($item);
                });
                
                $('body').append($dropdown);
            },
            error: function(xhr) {
                console.error('Error searching products:', xhr);
            }
        });
    });
    
    // Ensure dropdown is removed when modal is closed
    $('#editOrderModal').on('hidden.bs.modal', function() {
        $('.autocomplete-dropdown').remove();
    });
    
    // Ensure dropdown is removed when clicking on other form elements
    $(document).on('focus', 'input, select, textarea, button', function() {
        if (!$(this).is('input[name$="[item_number]"], input[name$="[product_name]"]')) {
            $('.autocomplete-dropdown').remove();
        }
    });
});
</script>

{% endblock %}






