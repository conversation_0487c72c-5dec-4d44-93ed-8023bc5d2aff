{% include 'html.twig' %}

{% block body %}

<style>
    @media (max-width: 575px) {
        .mobile-pagination {
            flex-direction: column;
            align-items: flex-start;
        }
        .pagination {
            margin-top: 10px;
        }
    }
</style>

{% include 'component/sidebar.twig' %}

        <div class="page-wrapper">

            <!-- Page Content-->
            <div class="page-content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="page-title-box d-md-flex justify-content-md-between align-items-center">
                                <h4 class="page-title">Orders</h4>
                                <div="">
                                    <ol class="breadcrumb mb-0">
                                        <li class="breadcrumb-item"><a href="#">Nurederm</a>
                                        </li><!--end nav-item-->
                                        <li class="breadcrumb-item"><a href="#">Ecommerce</a>
                                        </li><!--end nav-item-->
                                        <li class="breadcrumb-item active">Orders</li>
                                    </ol>
                                </div>
                            </div><!--end page-title-box-->
                        </div><!--end col-->
                    </div><!--end row-->
                    <div class="row mb-4">
                        <div class="col-lg-12">
                            <div class="card bg-primary">
                                <div class="card-body text-center py-4">
                                    <h1 style="font-size: 44px; color: white; font-weight: bold;">Sevkdesk Order Management</h1>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Orders Card -->
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h4 class="card-title">Orders</h4>
                                        </div><!--end col-->
                                        <div class="col-auto">
                                            <div class="d-flex gap-2">
{#                                                 <form action="" method="GET" class="d-flex gap-2">
                                                    <input type="hidden" name="per_page" value="{{ perPage }}">
                                                    <input type="text" name="search" class="form-control" placeholder="Ara..." value="{{ search }}">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </form> #}
                                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createOrderModal">
                                                    <i class="fas fa-plus me-1"></i> Add Order
                                                </button>
                                            </div>
                                        </div><!--end col-->
                                    </div>  <!--end row-->
                                </div><!--end card-header-->
                                <div class="card-body pt-0">
                                    <div class="table-responsive">
                                        <table class="table mb-0">
                                            <thead class="table-light">
                                              <tr>
                                                <th>Invoice #</th>
                                                <th>Customer</th>
                                                <th>Date</th>
                                                <th>Payment</th>
                                                <th>Status</th>
                                                <th>Total</th>
                                                <th class="text-end">Action</th>
                                              </tr>
                                            </thead>
                                            <tbody>
                                                {% for invoice in invoices %}
                                                <tr>
                                                    <td>
                                                        <a href="{{ route('order.details', {'id': invoice.id}) }}">#{{ invoice.invoice_number }}</a>
                                                        {% if invoice.problem %}
                                                            <i class="fas fa-exclamation-triangle text-warning ms-1 problem-icon" 
                                                               data-bs-toggle="tooltip" 
                                                               data-bs-placement="right" 
                                                               title="Backlog listesinde bulunmaktadır"
                                                               data-invoice-id="{{ invoice.id }}"
                                                               data-invoice-number="{{ invoice.invoice_number }}"></i>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <p class="d-inline-block align-middle mb-0">
                                                            <span class="d-block align-middle mb-0 product-name text-body">{{ invoice.customer.first_name }} {{ invoice.customer.last_name }}</span>
                                                            <span class="text-muted font-13">{{ invoice.company_name }}</span>
                                                        </p>
                                                    </td>
                                                    <td>{{ invoice.invoice_date|date('d/m/Y') }}</td>
                                                    <td>{{ invoice.iban ? 'Banking' : 'Other' }}</td>
                                                    <td>
                                                        {% if invoice.status == 0 %}
                                                            <span class="badge bg-warning-subtle text-warning">
                                                                <i class="fas fa-clock me-1"></i> Pending
                                                            </span>
                                                        {% elseif invoice.status == 1 %}
                                                            <span class="badge bg-success-subtle text-success">
                                                                <i class="fas fa-check me-1"></i> Approved
                                                            </span>
                                                  {% if invoice.shipmentPackages|length > 0 %}
                                                        <span class="badge bg-info-subtle text-info ms-1">
                                                            <i class="fas fa-truck me-1"></i> Shipped
                                                        </span>
                                                    {% endif %}
                                                        {% elseif invoice.status == 99 %}
                                                            <span class="badge bg-info-subtle text-danger">
                                                                <i class="fas fa-sync me-1"></i> Test Order
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-danger-subtle text-danger">
                                                                <i class="fas fa-times me-1"></i> Cancelled
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                    <td>€{{ invoice.total|number_format(2, '.', ',') }}</td>
                                                    <td class="text-end">
                                                        <a href="{{ route('order.details', {'id': invoice.id}) }}"><i class="fas fa-pen text-secondary fs-18"></i></a>
                                                        <a href="#" class="delete-invoice" data-id="{{ invoice.id }}"><i class="fas fa-trash-alt text-secondary fs-18"></i></a>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mt-3 mobile-pagination" style="flex-wrap: wrap;">
                                        <div class="d-flex align-items-center">
                                            <label class="me-2">Sayfa başına:</label>
                                            <select class="form-select form-select-sm" style="width: auto;" onchange="window.location.href='?per_page=' + this.value + '{% if search %}&search={{ search }}{% endif %}'">
                                                <option value="20" {% if perPage == 20 %}selected{% endif %}>20</option>
                                                <option value="50" {% if perPage == 50 %}selected{% endif %}>50</option>
                                                <option value="100" {% if perPage == 100 %}selected{% endif %}>100</option>
                                            </select>
                                        </div>
                                        <div>
                                            <nav>
                                                <ul class="pagination mb-0">
                                                    <li class="page-item {% if invoices.currentPage == 1 %}disabled{% endif %}">
                                                        <a class="page-link" href="?page={{ invoices.currentPage - 1 }}&per_page={{ perPage }}">Önceki</a>
                                                    </li>
                                                    
                                                    {% for i in range(1, invoices.lastPage) %}
                                                        <li class="page-item {% if invoices.currentPage == i %}active{% endif %}">
                                                            <a class="page-link" href="?page={{ i }}&per_page={{ perPage }}">{{ i }}</a>
                                                        </li>
                                                    {% endfor %}
                                                    
                                                    <li class="page-item {% if invoices.currentPage == invoices.lastPage %}disabled{% endif %}">
                                                        <a class="page-link" href="?page={{ invoices.currentPage + 1 }}&per_page={{ perPage }}">Sonraki</a>
                                                    </li>
                                                </ul>
                                            </nav>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- end col -->
                    </div> <!-- end row -->
                </div><!-- container -->

            </div>
            <!-- end page content -->
        </div>
        <!-- Create Order Modal -->
        <div class="modal fade" id="createOrderModal" tabindex="-1" aria-labelledby="createOrderModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="createOrderModalLabel">Create New Order</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createOrderForm">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <!-- Customer Select -->
                            <div class="mb-3">
                                <label class="form-label">Customer</label>
                                <select class="form-control" id="customerSelect" style="width: 100%">
                                </select>
                            </div>

                            <!-- Invoice Date -->
                            <div class="mb-3">
                                <label class="form-label">Invoice Date</label>
                                <input type="date" class="form-control" id="invoiceDate" value="{{ 'now'|date('Y-m-d') }}">
                            </div>

                            <!-- Address Section -->
                            <div class="mb-3">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="useCustomerAddress" checked>
                                    <label class="form-check-label" for="useCustomerAddress">
                                        Use customer address
                                    </label>
                                </div>
                                <textarea class="form-control" id="address" rows="3"></textarea>
                            </div>

                            <!-- IBAN -->
                            <div class="mb-3">
                                <label class="form-label">IBAN</label>
                                <input type="text" class="form-control" id="iban" readonly>
                            </div>

                            <!-- Items Section -->
                            <div class="border p-3 rounded mb-3">
                                <h6>Items</h6>
                                <div id="itemsContainer">
                                    <!-- Items will be added here -->
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="addItemBtn">
                                    <i class="fas fa-plus me-1"></i> Add Item
                                </button>
                            </div>

                            <!-- Total -->
                            <div class="text-end">
                                <h5>Total: €<span id="orderTotal">0.00</span></h5>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" id="saveOrderBtn">Create Order</button>
                    </div>
                </div>
            </div>
        </div>
{% endblock %}

{% block javascripts %}
<!-- Include necessary CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

<!-- Include necessary JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Problem icon click handler
    $(document).on('click', '.problem-icon', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const invoiceNumber = $(this).data('invoice-number');
        
        // Create and show modal
        const modalHtml = `
            <div class="modal fade" id="problemModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Backlog Problemi</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Fatura #${invoiceNumber} için backlog problemi çözüldü mü?</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hayır</button>
                            <button type="button" class="btn btn-primary resolve-problem" data-invoice-number="${invoiceNumber}">Evet, Çözüldü</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove any existing modal
        $('#problemModal').remove();
        
        // Add new modal to body
        $('body').append(modalHtml);
        
        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('problemModal'));
        modal.show();
    });
    
    // Resolve problem button click
    $(document).on('click', '.resolve-problem', function() {
        const invoiceNumber = $(this).data('invoice-number');
        
        $.ajax({
            url: '{{ route("backlog.resolve") }}',
            method: 'POST',
            data: {
                invoice_number: invoiceNumber,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    // Close modal
                    $('#problemModal').modal('hide');
                    
                    // Show success notification
                    const notificationHtml = `
                        <div class="position-fixed top-0 end-0 p-3" style="z-index: 1060">
                            <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="d-flex">
                                    <div class="toast-body">
                                        <i class="fas fa-check-circle me-2"></i> Problem başarıyla çözüldü.
                                    </div>
                                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    $('body').append(notificationHtml);
                    const toast = new bootstrap.Toast($('.toast'));
                    toast.show();
                    
                    // Remove the warning icon
                    $(`[data-invoice-number="${invoiceNumber}"].problem-icon`).fadeOut(300, function() {
                        $(this).remove();
                    });
                    
                    // Reload page after 1.5 seconds
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            },
            error: function(xhr) {
                alert('Error resolving problem: ' + (xhr.responseJSON?.message || 'Unknown error'));
            }
        });
    });

    // CSRF Token'ı ayarla
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    });

    // Initialize Select2
    $('#customerSelect').select2({
        dropdownParent: $('#createOrderModal'),
        ajax: {
            url: '{{ route("order.search") }}',
            type: 'GET',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                console.log('Search params:', params); // Debug log
                return {
                    term: params.term || ''
                };
            },
            beforeSend: function() {
                console.log('Making AJAX request...'); // Debug log
            },
            success: function(data) {
                console.log('Response data:', data); // Debug log
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error); // Debug log
            },
            processResults: function(data) {
                console.log('Processing results:', data); // Debug log
                return {
                    results: data.map(function(item) {
                        return {
                            id: item.id,
                            text: item.first_name + ' ' + item.last_name + (item.company ? ' (' + item.company + ')' : ''),
                            company: item.company,
                            address: item.address,
                            iban: item.iban
                        };
                    })
                };
            },
            cache: true
        },
        minimumInputLength: 2,
        placeholder: 'Search for a customer...',
        allowClear: true
    });

    // Debug için event listener ekleyelim
    $('#customerSelect').on('select2:open', function() {
        console.log('Select2 opened');
    });

    $('#customerSelect').on('select2:ajax:error', function(e) {
        console.error('Select2 AJAX error:', e);
    });

    // Address checkbox kontrolü
    $('#useCustomerAddress').change(function() {
        const addressField = $('#address');
        if (this.checked) {
            addressField.prop('readonly', true);
            // Eğer müşteri seçiliyse, müşteri adresini kullan
            const selectedData = $('#customerSelect').select2('data')[0];
            if (selectedData) {
                addressField.val(selectedData.address);
            }
        } else {
            addressField.prop('readonly', false);
        }
    });

    // Customer seçildiğinde
    $('#customerSelect').on('select2:select', function(e) {
        const data = e.params.data;
        $('#iban').val(data.iban);
        if ($('#useCustomerAddress').is(':checked')) {
            $('#address').val(data.address);
        }
    });

    // Add item butonu için template
    function getItemTemplate() {
        return `
            <div class="item-row border-bottom pb-2 mb-2">
                <div class="row g-2">
                    <div class="col-md-3">
                        <input type="text" class="form-control" name="items[][product_name]" placeholder="Product Name">
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control quantity" name="items[][quantity]" placeholder="Quantity">
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control price" name="items[][price]" placeholder="Price">
                    </div>
                    <div class="col-md-2">
                        <input type="number" class="form-control tax" name="items[][tax_rate]" placeholder="Tax %">
                    </div>
                    <div class="col-md-2">
                        <input type="text" class="form-control" name="items[][description]" placeholder="Description">
                    </div>
                    <div class="col-md-1">
                        <button type="button" class="btn btn-danger btn-sm remove-item"><i class="fas fa-times"></i></button>
                    </div>
                </div>
            </div>
        `;
    }

    // Add item click handler
    $('#addItemBtn').click(function() {
        $('#itemsContainer').append(getItemTemplate());
    });

    // Remove item
    $(document).on('click', '.remove-item', function() {
        $(this).closest('.item-row').remove();
        updateTotals();
    });

    // Calculate totals
    function calculateGrossAmount(quantity, price, taxRate) {
        const subtotal = quantity * price;
        const taxAmount = subtotal * (taxRate / 100);
        return subtotal + taxAmount;
    }

    function updateTotals() {
        let orderTotal = 0;
        $('.item-row').each(function() {
            const quantity = parseFloat($(this).find('.quantity').val()) || 0;
            const price = parseFloat($(this).find('.price').val()) || 0;
            const taxRate = parseFloat($(this).find('.tax').val()) || 0;

            const grossAmount = calculateGrossAmount(quantity, price, taxRate);
            orderTotal += grossAmount;
        });

        $('#orderTotal').text(orderTotal.toFixed(2));
    }

    // Update totals when inputs change
    $(document).on('input', '.quantity, .price, .tax', updateTotals);

    // Save Order
    $('#saveOrderBtn').click(function() {
        const formData = {
            _token: '{{ csrf_token() }}',
            customer_id: $('#customerSelect').val(),
            invoice_date: $('#invoiceDate').val(),
            address: $('#address').val(),
            iban: $('#iban').val(),
            total: parseFloat($('#orderTotal').text()),
            items: []
        };

        $('.item-row').each(function() {
            const quantity = parseFloat($(this).find('[name$="[quantity]"]').val()) || 0;
            const price = parseFloat($(this).find('[name$="[price]"]').val()) || 0;
            const taxRate = parseFloat($(this).find('[name$="[tax_rate]"]').val()) || 0;

            const item = {
                product_name: $(this).find('[name$="[product_name]"]').val(),
                quantity: quantity,
                price: price,
                tax_rate: taxRate,
                description: $(this).find('[name$="[description]"]').val(),
                gross_amount: calculateGrossAmount(quantity, price, taxRate)
            };
            formData.items.push(item);
        });

        $.ajax({
            url: '{{ route("order.store") }}',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    $('#createOrderModal').modal('hide');
                    window.location.reload();
                }
            },
            error: function(xhr) {
                alert('Error creating order: ' + xhr.responseJSON.message);
            }
        });
    });

    // Modal açıldığında formu sıfırla
    $('#createOrderModal').on('show.bs.modal', function () {
        $('#createOrderForm').trigger('reset');
        $('#customerSelect').val(null).trigger('change');
        $('#itemsContainer').empty();
        $('#orderTotal').text('0.00');
    });
});
</script>
{% endblock %}













