/* Autocomplete Dropdown Styling for Bootstrap Theme */

/* Base autocomplete dropdown styles - Light Mode */
.autocomplete-dropdown {
    position: absolute;
    max-height: 200px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    font-size: 0.875rem;
}

.autocomplete-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    color: #212529;
    background-color: #fff;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
}

.autocomplete-item:last-child {
    border-bottom: none;
}

.autocomplete-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.autocomplete-item-title {
    font-weight: 500;
    margin-bottom: 2px;
}

.autocomplete-item-subtitle {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Dark Mode Styling */
[data-bs-theme="dark"] .autocomplete-dropdown {
    background-color: #2b2b2b;
    border-color: #404040;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

[data-bs-theme="dark"] .autocomplete-item {
    color: #e9ecef;
    background-color: #2b2b2b;
    border-bottom-color: #404040;
}

[data-bs-theme="dark"] .autocomplete-item:hover {
    background-color: #404040;
    color: #fff;
}

[data-bs-theme="dark"] .autocomplete-item-subtitle {
    color: #adb5bd;
}

/* Mobile-specific autocomplete dropdown fixes */
@media (max-width: 768px) {
    .autocomplete-dropdown {
        /* Critical: Allow vertical scrolling only */
        touch-action: pan-y pinch-zoom !important;
        /* Enable momentum scrolling */
        -webkit-overflow-scrolling: touch !important;
        overflow-scrolling: touch !important;
        /* Contain scroll behavior */
        overscroll-behavior-y: contain !important;
        overscroll-behavior-x: none !important;
        /* Force hardware acceleration */
        -webkit-transform: translate3d(0, 0, 0) !important;
        transform: translate3d(0, 0, 0) !important;
        /* Optimize for scrolling */
        will-change: scroll-position !important;
        /* Ensure scrollable area */
        overflow-y: scroll !important;
        overflow-x: hidden !important;
        /* Better positioning */
        position: fixed !important;
        /* Prevent text selection during scroll */
        -webkit-user-select: none !important;
        user-select: none !important;
        /* Mobile-specific sizing */
        font-size: 0.8rem;
        max-height: 200px !important;
        min-height: 50px !important;
    }

    .autocomplete-item {
        /* Allow touch interaction on items */
        touch-action: manipulation !important;
        /* Prevent text selection issues on mobile */
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
        /* Ensure touch targets are large enough */
        min-height: 48px !important;
        display: flex !important;
        align-items: center !important;
        /* Better touch feedback */
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
        /* Prevent item from interfering with scroll */
        pointer-events: auto !important;
        /* Mobile-specific padding */
        padding: 12px 10px;
    }

    .autocomplete-item-subtitle {
        font-size: 0.7rem;
    }

    /* Ensure dropdown scrollbar is visible and usable on mobile */
    .autocomplete-dropdown::-webkit-scrollbar {
        width: 8px !important;
        -webkit-appearance: none !important;
    }

    .autocomplete-dropdown::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.3) !important;
        border-radius: 4px !important;
        -webkit-appearance: none !important;
    }

    .autocomplete-dropdown::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.1) !important;
        -webkit-appearance: none !important;
    }

    /* Fallback for browsers that don't support -webkit-overflow-scrolling */
    .autocomplete-dropdown[data-mobile-scroll="true"] {
        overflow: auto !important;
        -webkit-overflow-scrolling: touch !important;
        /* Force scrollable behavior */
        max-height: 200px !important;
        min-height: 50px !important;
    }
}

/* Responsive Design for smaller screens */
@media (max-width: 576px) {
    .autocomplete-dropdown {
        font-size: 0.75rem;
    }

    .autocomplete-item {
        padding: 10px 8px;
        min-height: 44px !important;
    }

    .autocomplete-item-subtitle {
        font-size: 0.65rem;
    }
}

/* Focus and Active States */
.autocomplete-item:focus,
.autocomplete-item.active {
    background-color: #0d6efd;
    color: #fff;
}

[data-bs-theme="dark"] .autocomplete-item:focus,
[data-bs-theme="dark"] .autocomplete-item.active {
    background-color: #0d6efd;
    color: #fff;
}

/* Loading State */
.autocomplete-loading {
    padding: 12px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

[data-bs-theme="dark"] .autocomplete-loading {
    color: #adb5bd;
}

/* No Results State */
.autocomplete-no-results {
    padding: 12px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
}

[data-bs-theme="dark"] .autocomplete-no-results {
    color: #adb5bd;
}

/* Enhanced visual separators */
.autocomplete-item + .autocomplete-item {
    border-top: 1px solid #f1f3f4;
}

[data-bs-theme="dark"] .autocomplete-item + .autocomplete-item {
    border-top: 1px solid #404040;
}

/* Smooth scrollbar styling */
.autocomplete-dropdown::-webkit-scrollbar {
    width: 6px;
}

.autocomplete-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.autocomplete-dropdown::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

[data-bs-theme="dark"] .autocomplete-dropdown::-webkit-scrollbar-track {
    background: #404040;
}

[data-bs-theme="dark"] .autocomplete-dropdown::-webkit-scrollbar-thumb {
    background: #6c757d;
}

[data-bs-theme="dark"] .autocomplete-dropdown::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* Email autocomplete dropdown için özel stil */
.email-autocomplete-dropdown {
    background-color: white;
    border: 1px solid #ccc;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 9999;
}

.email-autocomplete-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    color: #000000;
    font-weight: 500;
    background-color: white;
}

.email-autocomplete-item:hover {
    background-color: #f0f0f0;
    color: #000000;
}

/* Karanlık mod stilleri */
[data-bs-theme="dark"] .email-autocomplete-dropdown {
    background-color: #2b2b2b !important;
    border-color: #404040 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}

[data-bs-theme="dark"] .email-autocomplete-item {
    color: #e9ecef !important;
    background-color: #2b2b2b !important;
    border-bottom-color: #404040 !important;
}

[data-bs-theme="dark"] .email-autocomplete-item:hover {
    background-color: #404040 !important;
    color: #fff !important;
}

