/**
 * Shared functionality for inbound create and edit pages
 */

function initInboundForm(options = {}) {
    const defaults = {
        mode: 'create',
        inboundId: null,
        existingItems: []
    };
    
    const settings = {...defaults, ...options};
    
    // Add item to the form
    $('#addItemBtn').click(function() {
        addItemRow();
    });
    
    // Function to add item row
    function addItemRow(data = {}) {
        const defaults = {
            id: null,
            productNumber: '',
            quantity: 1
        };
        
        const item = {...defaults, ...data};
        
        const itemRow = `
            <div class="item-row mb-3 border-bottom pb-3" ${item.id ? 'data-item-id="' + item.id + '"' : ''}>
                <div class="row">
                    <div class="col-md-5 mb-2">
                        <label class="form-label">Product Number</label>
                        <input type="text" class="form-control product-number" name="product_numbers[]" value="${item.productNumber}" required>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label class="form-label">Quantity</label>
                        <input type="number" class="form-control item-quantity" name="quantities[]" min="1" value="${item.quantity}" required>
                    </div>
                    <div class="col-md-1 d-flex align-items-end mb-2">
                        <button type="button" class="btn btn-sm btn-outline-danger remove-item">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        $('#itemsContainer').append(itemRow);

        // Remove item handler
        $('.remove-item').off('click').on('click', function() {
            $(this).closest('.item-row').remove();
        });

        // Initialize autocomplete for the new product number input
        initProductAutocomplete();
    }
    
    // Excel import handler
    $('#excelImport').change(function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, {type: 'array'});
            
            // Get first sheet
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
            
            // Convert to JSON
            const jsonData = XLSX.utils.sheet_to_json(firstSheet);
            
            if (jsonData.length === 0) {
                alert('No data found in the Excel file');
                return;
            }
            
            // Check if required columns exist
            if (!jsonData[0].hasOwnProperty('productNumber') || !jsonData[0].hasOwnProperty('quantity')) {
                alert('Excel file must contain "productNumber" and "quantity" columns');
                return;
            }
            
            // Clear existing items if needed
            if (confirm('Do you want to replace existing items with imported ones?')) {
                $('#itemsContainer').empty();
            }
            
            // Add imported items
            jsonData.forEach(item => {
                addItemRow({
                    productNumber: item.productNumber,
                    quantity: item.quantity
                });
            });
        };
        reader.readAsArrayBuffer(file);
    });
    
    // If in edit mode, populate existing items
    if (settings.mode === 'edit' && settings.existingItems.length > 0) {
        // Clear container first
        $('#itemsContainer').empty();
        
        // Add each existing item
        settings.existingItems.forEach(item => {
            // Get product number from the product
            let productNumber = '';
            if (item.product && item.product.product_json) {
                try {
                    const productData = JSON.parse(item.product.product_json);
                    productNumber = productData[0]?.itemNumber || '';
                } catch (e) {
                    console.error('Error parsing product JSON:', e);
                }
            }
            
            addItemRow({
                id: item.id,
                productNumber: productNumber,
                quantity: item.quantity
            });
        });
    } else {
        // In create mode, add an empty row to start
        addItemRow();
    }
}

/**
 * Initialize autocomplete functionality for product number inputs
 */
function initProductAutocomplete() {
    // Product number search with autocomplete
    $(document).off('keyup', '.product-number').on('keyup', '.product-number', function() {
        const $input = $(this);
        const term = $input.val().trim();

        if (term.length < 2) {
            $('.autocomplete-dropdown').remove();
            return;
        }

        // Show loading state
        $('.autocomplete-dropdown').remove();
        const $loadingDropdown = createLoadingDropdown($input);
        $('body').append($loadingDropdown);

        // Search for products
        $.ajax({
            url: '/product/search',
            type: 'GET',
            data: {
                term: term,
                searchType: 'item' // Search by item number primarily
            },
            success: function(data) {
                $('.autocomplete-dropdown').remove();

                if (data.length === 0) {
                    const $noResultsDropdown = createNoResultsDropdown($input);
                    $('body').append($noResultsDropdown);
                    return;
                }

                const $dropdown = createAutocompleteDropdown($input, data);
                $('body').append($dropdown);
            },
            error: function(xhr) {
                $('.autocomplete-dropdown').remove();
                console.error('Error searching products:', xhr);
            }
        });
    });

    // Close dropdown when clicking outside
    $(document).off('click.autocomplete').on('click.autocomplete', function(e) {
        if (!$(e.target).closest('.autocomplete-dropdown, .product-number').length) {
            $('.autocomplete-dropdown').remove();
        }
    });

    // Close dropdown when focusing other elements
    $(document).off('focus.autocomplete', 'input, select, textarea, button').on('focus.autocomplete', 'input, select, textarea, button', function() {
        if (!$(this).hasClass('product-number')) {
            $('.autocomplete-dropdown').remove();
        }
    });

    // Close dropdown when product input loses focus (with delay for dropdown clicks)
    $(document).off('blur.autocomplete', '.product-number').on('blur.autocomplete', '.product-number', function() {
        setTimeout(function() {
            if (!$('.autocomplete-dropdown:hover').length) {
                $('.autocomplete-dropdown').remove();
            }
        }, 150);
    });
}

/**
 * Create autocomplete dropdown with search results
 */
function createAutocompleteDropdown($input, data) {
    const $dropdown = $('<div class="autocomplete-dropdown"></div>');

    // Position dropdown correctly
    const inputPos = $input.offset();
    $dropdown.css({
        top: inputPos.top + $input.outerHeight(),
        left: inputPos.left,
        width: $input.outerWidth()
    });

    data.forEach(function(item) {
        const $item = $('<div class="autocomplete-item"></div>');

        // Create item content with title and subtitle
        const $title = $('<div class="autocomplete-item-title"></div>').text(item.itemNumber);
        const $subtitle = $('<div class="autocomplete-item-subtitle"></div>').text(item.description);

        $item.append($title).append($subtitle);

        $item.on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Populate the input field with the selected item number
            $input.val(item.itemNumber);

            // Remove dropdown
            $('.autocomplete-dropdown').remove();

            // Focus next input (quantity) for better UX
            $input.closest('.item-row').find('.item-quantity').focus();
        });

        $dropdown.append($item);
    });

    return $dropdown;
}

/**
 * Create loading dropdown
 */
function createLoadingDropdown($input) {
    const $dropdown = $('<div class="autocomplete-dropdown"></div>');
    const $loading = $('<div class="autocomplete-loading">Searching...</div>');

    // Position dropdown correctly
    const inputPos = $input.offset();
    $dropdown.css({
        top: inputPos.top + $input.outerHeight(),
        left: inputPos.left,
        width: $input.outerWidth()
    });

    $dropdown.append($loading);
    return $dropdown;
}

/**
 * Create no results dropdown
 */
function createNoResultsDropdown($input) {
    const $dropdown = $('<div class="autocomplete-dropdown"></div>');
    const $noResults = $('<div class="autocomplete-no-results">No products found</div>');

    // Position dropdown correctly
    const inputPos = $input.offset();
    $dropdown.css({
        top: inputPos.top + $input.outerHeight(),
        left: inputPos.left,
        width: $input.outerWidth()
    });

    $dropdown.append($noResults);
    return $dropdown;
}