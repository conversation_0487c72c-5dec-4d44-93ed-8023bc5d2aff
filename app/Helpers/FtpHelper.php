<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use League\Flysystem\Filesystem;
use League\Flysystem\PhpseclibV3\SftpConnectionProvider;
use League\Flysystem\PhpseclibV3\SftpAdapter;
use League\Flysystem\FilesystemAdapter;

class FtpHelper
{
    /**
     * PDF dosyasını SFTP sunucusuna yükler
     * 
     * @param string $filename Yüklenecek dosyanın adı (örn: RE-2165.pdf)
     * @return bool Yükleme başarılı ise true, değilse false döner
     */
    public static function uploadInvoicePdf(string $filename): bool
    {
        try {
            // Yerel dosya yolu
            $localPath = public_path('invoices/' . $filename);
            
            // Dosya yoksa hata döndür
            if (!file_exists($localPath)) {
                \Log::error("Yüklenecek dosya bulunamadı: {$localPath}");
                return false;
            }
            
            // SFTP disk yapılandırması
            config(['filesystems.disks.sftp' => [
                'driver' => 'sftp',
                'host' => 'ftp1.gedak.de',
                'username' => 'nurederm',
                'password' => 'Jolaxaio84',
                'port' => 22,
                'root' => '/',
                'timeout' => 30,
            ]]);
            
            // Dosyayı yükle
            $fileContent = file_get_contents($localPath);
            Storage::disk('sftp')->put($filename, $fileContent);
            
            \Log::info("Dosya başarıyla FTP sunucusuna yüklendi: {$filename}");
            return true;
            
        } catch (\Exception $e) {
            \Log::error("FTP yükleme hatası: " . $e->getMessage());
            return false;
        }
    }
}
