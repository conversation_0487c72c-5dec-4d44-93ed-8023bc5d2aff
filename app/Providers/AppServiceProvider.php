<?php

namespace App\Providers;

use App\Services\Notification\NotificationService;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(NotificationService::class, function ($app) {
            return new NotificationService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        View::composer('*', function ($view) {
            $view->with('authUser', auth()->user());
        });

        // Twig'e file_exists fonksiyonunu ekle
        \Twig::addFunction(new \Twig\TwigFunction('file_exists', function ($path) {
            return file_exists(public_path($path));
        }));

        // Twig'e asset fonksiyonunu ekle (eğer zaten eklenmemişse)
        \Twig::addFunction(new \Twig\TwigFunction('asset', function ($path) {
            return asset($path);
        }));
    }
}

