<?php

namespace App\Http\Controllers;

use App\Models\BacklogList;
use Illuminate\Http\Request;

class BacklogController extends Controller
{
    public function resolve(Request $request)
    {
        $request->validate([
            'invoice_number' => 'required|string'
        ]);
        
        $invoiceNumber = $request->input('invoice_number');
        
        $updated = BacklogList::where('invoice_number', $invoiceNumber)
            ->update(['resolved' => 1]);
            
        return response()->json([
            'success' => true,
            'message' => 'Backlog issue marked as resolved',
            'updated' => $updated
        ]);
    }
}