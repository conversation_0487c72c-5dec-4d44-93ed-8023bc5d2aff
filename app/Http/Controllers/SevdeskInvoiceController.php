<?php

namespace App\Http\Controllers;

use App\Models\SevdeskInvoice;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SevdeskInvoiceController extends Controller
{
    public function index()
    {
        $invoices = SevdeskInvoice::orderBy('created_at', 'desc')->get();
        return view('sevdesk.index', compact('invoices'));
    }

    public function create()
    {
        $products = Product::all();
        $customers = Customer::select('id', 'client_id', 'first_name', 'last_name', 'company', 'address', 'house_number', 'zip_code', 'city', 'country')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        $sevUsers = [
            (object) ['id' => 968567, 'firstName' => 'Erkin', 'lastName' => 'Ergüney']
        ];

        $lastInvoiceNumber = SevdeskInvoice::where('invoice_number', 'LIKE', 'NRD-%')
            ->max(DB::raw('CAST(SUBSTRING(invoice_number, 5) AS UNSIGNED)'));

        $lastNumber = (int)($lastInvoiceNumber ?? 999);
        $nextNumber = max($lastNumber + 1, 1000);
        $nextInvoiceNumber = 'NRD-' . $nextNumber;

        return view('sevdesk.create', compact('products', 'customers', 'sevUsers', 'nextInvoiceNumber'));
    }

    public function store(Request $request)
    {
        try {
            $sevdesk_token = env('SEVDESK_API_TOKEN');

            // Check if this is just saving a template
            $isTemplate = $request->query('isTemplate') === 'true';
            $invoiceData = $request->all();
            $old_suffix = $request->input('invoice.invoiceNumberSuffix') ?? $request->query('suffix');
            $suffix = $old_suffix;
            if (!is_numeric($suffix) || $suffix < 1000) {
                $lastInvoiceNumber = SevdeskInvoice::where('invoice_number', 'LIKE', 'NRD-%')
                    ->max(DB::raw('CAST(SUBSTRING(invoice_number, 5) AS UNSIGNED)'));

                $lastNumber = (int)($lastInvoiceNumber ?? 999);

                $suffix = $lastNumber + 1;
            }
            $contactId = $invoiceData['invoice']['contact']['id'] ?? null;
            if ($contactId) {
                $customer = Customer::where('client_id', $contactId)->first();
                if ($customer) {
                    $addressParts = [];

                    $fullName = trim(($customer->first_name ?? '') . ' ' . ($customer->last_name ?? ''));
                    if ($fullName) {
                        $addressParts[] = $fullName;
                    }

                    // Add company
                    if ($customer->company) {
                        $addressParts[] = $customer->company;
                    }

                    // Add street address
                    $streetAddress = trim(($customer->address ?? '') . ' ' . ($customer->house_number ?? ''));
                    if ($streetAddress) {
                        $addressParts[] = $streetAddress;
                    }

                    // Add ZIP and city
                    $zipCity = trim(($customer->zip_code ?? '') . ' ' . ($customer->city ?? ''));
                    if ($zipCity) {
                        $addressParts[] = $zipCity;
                    }

                    // Add country
                    if ($customer->country) {
                        $addressParts[] = $customer->country;
                    }

                    $invoiceData['invoice']['address'] = implode("\n", $addressParts);
                }
            }

            $fullInvoiceNumber = 'NRD-' . $suffix;
            $invoiceData['invoice']['invoiceNumber'] = $fullInvoiceNumber;
            $invoiceData['invoice']['header'] = "Rechnung Nr. " . $fullInvoiceNumber;

            Log::info('Sevdesk invoice data: ' . json_encode($invoiceData));

            if ($isTemplate) {
                // Save or update template
                $invoice = SevdeskInvoice::updateOrCreate(
                    ['invoice_number' => $fullInvoiceNumber],
                    [
                        'sevdesk_json' => $invoiceData,
                        'invoice_contact_id' => $invoiceData['invoice']['contact']['id'] ?? null,
                        'status' => $invoiceData['invoice']['status'] ?? null,
                        'total' => collect($invoiceData['invoicePosSave'] ?? [])->sum('priceGross'),
                        'is_template' => true,
                    ]
                );

                return response()->json([
                    'success' => true,
                    'message' => 'Template saved successfully',
                    'invoice' => $invoice,
                ]);
            }
            $stock_result = $this->checkStock($invoiceData['invoicePosSave'] ?? []);
            if ($stock_result['success'] === false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stock check failed',
                    'error' => $stock_result['issues']
                ], 400);
            }
            // Make API request to Sevdesk
            $response = Http::withHeaders([
                'Authorization' => $sevdesk_token,
                'Content-Type' => 'application/json'
            ])->post('https://my.sevdesk.de/api/v1/Invoice/Factory/saveInvoice', $invoiceData);

            Log::info('Sevdesk API response: ' . $response->body());

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create invoice in Sevdesk',
                    'error' => $response->body()
                ], 500);
            }

            $responseData = $response->json();

            $invoiceObj = $responseData['objects']['invoice'] ?? [];
            $actualInvoiceNumber = $invoiceObj['invoiceNumber'] ?? $fullInvoiceNumber;

            $invoice = SevdeskInvoice::updateOrCreate(
                ['invoice_number' => $actualInvoiceNumber],
                [
                    'sevdesk_json' => $invoiceData,
                    'sevdesk_response' => $responseData,
                    'invoice_contact_id' => $invoiceObj['contact']['id'] ?? null,
                    'status' => $invoiceObj['status'] ?? null,
                    'total' => $invoiceObj['sumGross'] ?? 0,
                    'is_template' => false,
                ]
            );
            if (
                $old_suffix != $suffix &&
                !empty($old_suffix) &&
                $old_suffix != $actualInvoiceNumber &&
                !empty($invoiceData['invoice']['invoiceNumberSuffix'])
            ) {
                $oldTemplateInvoiceNumber = 'NRD-' . $old_suffix;
                SevdeskInvoice::where('invoice_number', $oldTemplateInvoiceNumber)
                    ->where('is_template', true)
                    ->delete();
            }

            return response()->json([
                'success' => true,
                'message' => 'Invoice created successfully',
                'invoice' => $invoice
            ]);
        } catch (\Exception $e) {
            Log::error('Sevdesk invoice creation error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error creating invoice: ' . $e->getMessage()
            ], 500);
        }
    }
    
   public function checkStock($invoicePosSave)
    {
        try{
            $stock_url = 'https://rest-api.portica.de/nurederm/api/stock';
        $stock_data = Http::withHeaders([
            'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
            'Content-Type' => 'application/json',
            'Accept' => '*/*',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Connection' => 'keep-alive',
            'User-Agent' => 'PostmanRuntime/7.29.0',
            'Host' => 'rest-api.portica.de'
        ])->get($stock_url);

        $stockIssues = [];

        foreach ($invoicePosSave as $item) {
            $item_number = $item['itemNumber'] ?? null;
            if (!$item_number) {
                $stockIssues[] = [
                    'item' => $item,
                    'issue' => 'Item number is missing: ' . json_encode($item),
                ];
                continue;
            }

            $quantity = $item['quantity'] ?? 0;
            $stock_data_array = $stock_data->json()['data'] ?? [];

            $item_in_stock = null;

            foreach ($stock_data_array as $stock_item) {
                if ($stock_item['productNumber'] === $item_number) {
                    $item_in_stock = $stock_item;
                    break;
                }
            }

            if (!$item_in_stock) {
                $stockIssues[] = [
                    'item' => $item,
                    'issue' => "Item {$item_number} not found in stock data",
                ];
            } elseif ($item_in_stock['stock'] < $quantity) {
                $stockIssues[] = [
                    'item' => $item,
                    'issue' => "Insufficient stock for item {$item_number}: available {$item_in_stock['stock']}, needed {$quantity}",
                ];
            }
        }

        if (!empty($stockIssues)) {
            return [
                'success' => false,
                'issues' => $stockIssues
            ];
        }

        return ['success' => true];
        }
        catch (\Exception $e) {
            Log::error('Stock check error: ' . $e->getMessage());
            return [
                'success' => false,
                'issues' => [['issue' => 'Portica API Error']]
            ];
        }
    }

    public function search(Request $request)
    {
        $term = $request->get('term');

        $customers = Customer::where('first_name', 'LIKE', "%{$term}%")
            ->orWhere('last_name', 'LIKE', "%{$term}%")
            ->orWhere('company', 'LIKE', "%{$term}%")
            ->orWhere('email', 'LIKE', "%{$term}%")
            ->select('id', 'client_id', 'first_name', 'last_name', 'company', 'address', 'house_number', 'zip_code', 'city', 'country', 'email')
            ->limit(10)
            ->get();

        return response()->json($customers);
    }

    public function show($id)
    {
        $invoice = SevdeskInvoice::findOrFail($id);
        return view('sevdesk.show', compact('invoice'));
    }

    public function useTemplate($id)
    {
        $template = SevdeskInvoice::where('id', $id)
            ->where('is_template', true)
            ->firstOrFail();

        $products = Product::all();
        $customers = Customer::select('id', 'client_id', 'first_name', 'last_name', 'company', 'address', 'house_number', 'zip_code', 'city', 'country')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        $sevUsers = [
            (object) ['id' => 968567, 'firstName' => 'Erkin', 'lastName' => 'Ergüney']
        ];

        $nextInvoiceNumber = $template->invoice_number;

        // Extract template data from sevdesk_json
        $templateData = $template->sevdesk_json;

        // Find the matching customer from template contact ID
        $selectedCustomer = null;
        if (isset($templateData['invoice']['contact']['id'])) {
            $contactId = $templateData['invoice']['contact']['id'];
            $selectedCustomer = Customer::where('client_id', $contactId)
                ->select('id', 'client_id', 'first_name', 'last_name', 'company', 'address', 'house_number', 'zip_code', 'city', 'country')
                ->first();
        }

        return view('sevdesk.create', compact('products', 'customers', 'sevUsers', 'nextInvoiceNumber', 'templateData', 'selectedCustomer'));
    }

    public function cancel($id)
    {

        $invoice = SevdeskInvoice::findOrFail($id);

        if ($invoice->is_template) {
            return response()->json(['success' => false, 'message' => 'Cannot cancel a template invoice.'], 400);
        }

        if ($invoice->is_cancelled) {
            return response()->json(['success' => false, 'message' => 'This invoice is already cancelled.'], 400);
        }


        $response = $this->cancelFromSevdesk($invoice);

        if (!$response) {
            return response()->json(['success' => false, 'message' => 'Failed to cancel invoice in Sevdesk.'], 500);
        }
        
        $invoice->is_cancelled = true;
        $invoice->save();

       

        return response()->json(['success' => true, 'message' => 'Invoice cancelled successfully.']);
    }
    public function destroy($id)
    {
        try {
            $invoice = SevdeskInvoice::findOrFail($id);

            // Check if it's a template - templates can be deleted directly
            if ($invoice->is_template) {
                $invoice->delete();
                return response()->json([
                    'success' => true,
                    'message' => 'Template deleted successfully.'
                ]);
            }

            // For regular invoices, check if it's already cancelled
            if ($invoice->is_cancelled) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete a cancelled invoice. Cancelled invoices should be kept for record purposes.'
                ], 400);
            }
            // Delete from local database
            $invoice->delete();

            return response()->json([
                'success' => true,
                'message' => 'Invoice deleted successfully.'
            ]);

        } catch (\Exception $e) {
            Log::error('Error deleting SevDesk invoice: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    private function cancelFromSevdesk(SevdeskInvoice $invoice)
    {
        $sevdesk_token = env('SEVDESK_API_TOKEN');
        $response = Http::withHeaders([
            'Authorization' => $sevdesk_token,
            'Content-Type' => 'application/json'
        ])->post("https://my.sevdesk.de/api/v1/Invoice/{$invoice->id}/cancelInvoice");
        return $response->successful();
    }
}