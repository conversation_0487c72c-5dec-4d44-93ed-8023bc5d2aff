<?php

namespace App\Http\Controllers;

use App\Enums\ShipmentStatus;
use App\Http\Controllers\Controller;
use App\Models\Invoice;
use App\Models\InvoiceShipmentStatus;
use App\Models\InvoiceShipmentPackage;
use App\Services\Notification\NotificationService;
use App\Services\Whatsapp\NotificationType;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class OrderCargoStatusController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Cron job tarafından tetiklenecek fonksiyon
     */
   public function updateShipmentStatuses()
    {
        $invoices = Invoice::where('status', 1)->get();

        foreach ($invoices as $invoice) {
            try {
                if ($invoice->shipmentPackages()->exists()) {
                    continue;
                }

                $url = "https://rest-api.portica.de/nurederm/api/order/{$invoice->invoice_number}";

                $response = Http::withHeaders([
                    'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
                    'Content-Type' => 'application/json',
                    'Accept' => '*/*',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Connection' => 'keep-alive',
                    'User-Agent' => 'PostmanRuntime/7.29.0',
                    'Host' => 'rest-api.portica.de'
                ])->get($url);

                if ($response->successful()) {
                    $data = $response->json('data');

                    if ($data) {
                        $hasValidPackage = collect($data['packages'])->contains(function ($pkg) {
                            return !is_null($pkg['carrier']);
                        });

                        if (!$hasValidPackage) {
                            continue;
                        }
                        $packageMessageData = [];

                        DB::transaction(function () use ($invoice, $data, &$packageMessageData) {
                            $invoice->shipmentPackages()->delete();

                            foreach ($data['packages'] as $package) {
                                InvoiceShipmentPackage::create([
                                    'invoice_id'       => $invoice->id,
                                    'package_number'   => $package['packageNumber'],
                                    'last_checked_at'  => now(),
                                ]);

                                $packageMessageData[] = [
                                    'order_number'  => $invoice->invoice_number,
                                    'tracking_link' => $package['trackingLink'] ?? 'N/A',
                                ];
                            }
                        });

                        $this->notificationService->notifyUser($invoice, NotificationType::SHIPPED, [
                            'packages'        => $packageMessageData,
                            'invoice_number'  => $invoice->invoice_number,
                            'customer_id'     => $invoice->customer_id,
                        ]);
                    }
                } else {
                    Log::warning("API response unsuccessful for invoice: {$invoice->invoice_number}");
                }
            } catch (Exception $e) {
                Log::error("Error while processing invoice: {$invoice->invoice_number}. Message: " . $e->getMessage());
            }
        }
    }

    public function trackShipmentStatus($token)
    {
        if ($token !== '066d961f862765db6ec0ba2de97c00cf') return;
        set_time_limit(0);
        $packages = InvoiceShipmentPackage::all();

        foreach ($packages as $package) {
            if ($package->package_number === null || $package->package_number === '') {
                continue;
            }
            if ($package->status === 'ZU') {
                continue;
            }

            $url = 'https://api-eu.dhl.com/track/shipments?trackingNumber=' . $package->package_number;

            $trackingData = $this->getTrackingDetails($package->package_number);

            if ($trackingData && isset($trackingData['shipments'][0])) {
                $shipment = $trackingData['shipments'][0];
                $status = $shipment['status'] ?? null;

                $statusCode = $status['status'] ?? null;                

                if ($statusCode !== null) {
                $isValidStatus = collect(ShipmentStatus::cases())
                    ->contains(fn($enum) => $enum->value === $statusCode);

                if ($isValidStatus) {
                    $statusEnum = ShipmentStatus::from($statusCode);

                    $trackedStatuses = [ShipmentStatus::DELIVERED_TO_AGENCY, ShipmentStatus::DELIVERED_SUCCESSFULLY];

                    if (in_array($statusEnum, $trackedStatuses)) {
                        if ($package->status !== null && $package->status !== $statusCode) {
                            $this->notificationService->notifyUser($package->invoice, NotificationType::SHIPMENT_STATUS_CHANGED, [
                                'customer_id' => $package->invoice->customer_id,
                                'invoice_number'  => $package->invoice->invoice_number,
                                'package_number'  => $package->package_number,
                                'current_status'  => $statusEnum->getMessage(),
                                'tracking_service_url'   => $shipment['serviceUrl'],
                            ]);
                        }

                        $package->update([
                            'status' => $statusCode,
                            'last_checked_at' => now(),
                        ]);
                    }
                }
            }
            } else {
            }
             sleep(6);  
        }
    }


    private function getTrackingDetails($packageNumber){
        try {
            $baseUrl = 'https://api-eu.dhl.com/track/shipments';

            $url = $baseUrl . '?trackingNumber=' . $packageNumber;

            $response = Http::withHeaders([
                'DHL-API-Key' => env('DHL_API_KEY'),
            ])->get($url);

            if ($response->successful()) {
                return $response->json();
            } else {
                return null;
            }
        } catch (Exception $e) {
            return null;
        }
                
    }
}
