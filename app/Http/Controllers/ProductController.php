<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use App\Models\ProductArchive;
use Illuminate\Support\Facades\Http;
use App\Models\BacklogList;
use Illuminate\Support\Facades\Log;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $selectedBrand = $request->query('brand');
        $search = $request->query('search');
        $perPage = $request->input('per_page', 50);
        
        // Get all unique brands from the database
        $brands = Product::distinct()->pluck('brand')->filter()->values()->toArray();
        
        // Base query
        $query = Product::query();
        
        // Apply brand filter if selected
        if ($selectedBrand === 'others') {
            // Get products with null or empty brand
            $query->where(function($q) {
                $q->whereNull('brand')
                  ->orWhere('brand', '');
            });
        } elseif ($selectedBrand) {
            // Get products with the selected brand
            $query->where('brand', $selectedBrand);
        }
        
        // Apply search filter if provided
        if ($search) {
            $searchLower = strtolower($search);
            $query->where(function($q) use ($searchLower) {
                $q->whereRaw('LOWER(JSON_EXTRACT(product_json, "$[0].itemNumber")) LIKE ?', ['%' . $searchLower . '%'])
                  ->orWhereRaw('LOWER(JSON_EXTRACT(product_json, "$[0].description")) LIKE ?', ['%' . $searchLower . '%']);
            });
        }
        
        // Get products
        $products = $query->get();
        
        return view('product.index', compact('products', 'brands', 'selectedBrand', 'search', 'perPage'));
    }

    public function store(Request $request)
    {
        try {
            $productData = $request->json()->all();
            $rawProductData = $productData;

            if ($productData[0]['expiryDate'] == null) {
                $productData[0]['expiryDate'] = 'NULL';
            }

            if ($productData[0]['country'] == null) {
                $productData[0]['country'] = 'DE';
            }

            $productData[0]['vatKind'] = intval([
                '0' => 1,     
                '7' => 3,    
                '19' => 2     
            ][$productData[0]['vatKind']] ?? $productData[0]['vatKind']);

            // API'ye gönder
            $apiResponse = Product::syncWithApi($productData);

            // API yanıtını kontrol et
            $responseBody = json_decode($apiResponse->getBody(), true);
            if ($apiResponse->getStatusCode() !== 201 || !str_contains($responseBody['message'], 'Operation successful')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to sync with external API'
                ], 500);
            }

            // API başarılıysa veritabanına kaydet
            $product = Product::create([
                'product_json' => json_encode($rawProductData)
            ]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }


    public function storeTotal(Request $request)
    {
        foreach ($request->json()->all() as $productData) {
            if($productData['expiryDate'] == null) {
                $productData['expiryDate'] = 'NULL';
            }
            if($productData['country'] == null) {
                $productData['country'] = 'DE';
            }
            // $productData[0]['ean'] int to string

            if(is_string($productData['width'])) {
                $productData['width'] = (float)$productData['width'];
                if(floor($productData['width']) == $productData['width']) {
                    $productData['width'] += 0.01;
                }
            }
            if(is_string($productData['height'])) {
                $productData['height'] = (float)$productData['height'];
                if(floor($productData['height']) == $productData['height']) {
                    $productData['height'] += 0.01;
                }
            }
            if(is_string($productData['length'])) {
                $productData['length'] = (float)$productData['length'];
                if(floor($productData['length']) == $productData['length']) {
                    $productData['length'] += 0.01;
                }
            }
            if(is_string($productData['weight'])) {
                $productData['weight'] = (float)$productData['weight'];
                if(floor($productData['weight']) == $productData['weight']) {
                    $productData['weight'] += 0.01;
                }
            }
            if(is_string($productData['priceNet'])) {
                $productData['priceNet'] = (float)$productData['priceNet'];
                if(floor($productData['priceNet']) == $productData['priceNet']) {
                    $productData['priceNet'] += 0.1;
                }
            }
            if(is_string($productData['priceGross'])) {
                $productData['priceGross'] = (float)$productData['priceGross'];
                if(floor($productData['priceGross']) == $productData['priceGross']) {
                    $productData['priceGross'] += 0.1;
                }
            }
            if(is_string($productData['purchasePrice'])) {
                $productData['purchasePrice'] = (float)$productData['purchasePrice'];
                if(floor($productData['purchasePrice']) == $productData['purchasePrice']) {
                    $productData['purchasePrice'] += 0.1;
                }
            }
 
            if($productData['tariffNumber'] == null) {
                $productData['tariffNumber'] = 'NULL';
            }
            
            $productData['ean'] = (string)$productData['ean'];
            $productData['tariffNumber'] = (string)$productData['tariffNumber'];


            try {
                // Önce API'ye gönder
                // put in array
                $productData = [$productData];
                
                $apiResponse = Product::syncWithApi($productData);
                
                // API yanıtını kontrol et
                $responseBody = json_decode($apiResponse->getBody(), true);
                if ($apiResponse->getStatusCode() !== 201 || !str_contains($responseBody['message'], 'Operation successful')) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to sync with external API'
                    ], 500);
                }

                $productArray = [$productData];
                // API başarılıysa veritabanına kaydet

                Product::updateOrCreate([
                    'product_json' => json_encode($productArray)
                ]);
                echo 'Product with item number ' . $productData['itemNumber'] . ' has been created or updated.';
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }
        }
        return response()->json(['success' => true]);
    }
        


    public function update(Request $request, $id)
    {
        try {
            $productData = $request->json()->all();
            $rawProductData = $productData;

            if($productData[0]['expiryDate'] == null) {
                $productData[0]['expiryDate'] = 'NULL';
            }

            $productData[0]['vatKind'] = intval([
                '0' => 1,     
                '7' => 3,    
                '19' => 2     
            ][$productData[0]['vatKind']] ?? $productData[0]['vatKind']);
            
            // Önce API'ye gönder
            $apiResponse = Product::syncWithApi($productData);

            if($productData[0]['country'] == null) {
                $productData[0]['country'] = 'DE';
            }
            
            // API yanıtını kontrol et
            $responseBody = json_decode($apiResponse->getBody(), true);
            if ($apiResponse->getStatusCode() !== 201 || !str_contains($responseBody['message'], 'Operation successful')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to sync with external API'
                ], 500);
            }

            // API başarılıysa veritabanını güncelle
            $product = Product::findOrFail($id);
            $product->update([
                'product_json' => json_encode($rawProductData)
            ]);

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function checkItemNumber(Request $request)
    {
        $itemNumber = $request->input('itemNumber');
        
        $exists = Product::whereRaw('JSON_EXTRACT(product_json, "$[0].itemNumber") = ?', [$itemNumber])->exists();
        
        return response()->json([
            'exists' => $exists
        ]);
    }

    public function search(Request $request)
    {
        $term = $request->get('term');
        $searchType = $request->get('searchType', 'name'); // Default to name search
        if (empty($term) || strlen($term) < 2) {
            return response()->json([]);
        }

        $products = [];

        // Get all products
        $allProducts = Product::all();

        $stock_data = Http::withHeaders([
            'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
            'Content-Type' => 'application/json',
            'Accept' => '*/*',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Connection' => 'keep-alive',
            'User-Agent' => 'PostmanRuntime/7.29.0',
            'Host' => 'rest-api.portica.de'
        ])->get('https://rest-api.portica.de/nurederm/api/stock')->json();

        // Create a lookup array for stock data by productNumber for faster access
        $stockLookup = [];
        if (isset($stock_data['data']) && is_array($stock_data['data'])) {
            foreach ($stock_data['data'] as $stockItem) {
                if (isset($stockItem['productNumber']) && isset($stockItem['stock'])) {
                    $stockLookup[$stockItem['productNumber']] = $stockItem['stock'];
                }
            }
        }

        foreach ($allProducts as $product) {
            $productData = json_decode($product->product_json, true)[0] ?? null;

            if (!$productData) continue;

            $matchFound = false;

            if ($searchType === 'name' && isset($productData['description'])) {
                $matchFound = stripos($productData['description'], $term) !== false;
            } elseif ($searchType === 'item' && isset($productData['itemNumber'])) {
                $matchFound = stripos($productData['itemNumber'], $term) !== false;
            } else {
                // Fallback to search in both fields
                $matchFound =
                    (isset($productData['description']) && stripos($productData['description'], $term) !== false) ||
                    (isset($productData['itemNumber']) && stripos($productData['itemNumber'], $term) !== false);
            }

            if ($matchFound) {
                // Get stock information by matching itemNumber with productNumber
                $itemNumber = $productData['itemNumber'] ?? '';
                $stock = isset($stockLookup[$itemNumber]) ? $stockLookup[$itemNumber] : 0;

                $products[] = [
                    'id' => $product->id,
                    'description' => $productData['description'] ?? '',
                    'itemNumber' => $itemNumber,
                    'priceGross' => $productData['priceGross'] ?? 0,
                    'priceNet' => $productData['priceNet'] ?? 0,
                    'stock' => $stock
                ];
            }
        }

        return response()->json($products);
    }

    public function archive($id)
    {
        try {
            $product = Product::findOrFail($id);
            
            // Ürünü arşive taşı
            ProductArchive::create([
                'product_json' => $product->product_json,
                'brand' => $product->brand
            ]);
            
            // Ürünü sil
            $product->delete();
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function showArchives()
    {
        $archivedProducts = ProductArchive::all();
        $brands = Product::distinct()->pluck('brand')->filter()->values()->toArray();
        
        return view('product.archives', compact('archivedProducts', 'brands'));
    }

    public function restoreFromArchive($id)
    {
        try {
            $archivedProduct = ProductArchive::findOrFail($id);
            
            // Arşivden ürünler tablosuna taşı
            Product::create([
                'product_json' => $archivedProduct->product_json,
                'brand' => $archivedProduct->brand
            ]);
            
            // Arşivden sil
            $archivedProduct->delete();
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function porticaDaily(Request $request) {
        $ip = $request->ip();
        $allowedIps = ['*************'];

        if (!in_array($ip, $allowedIps)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }
        try {
            $data = $request->json()->all();
            
            if (is_array($data)) {
                $invoiceNumbers = $data;
                
                foreach ($invoiceNumbers as $invoiceNumber) {
                    BacklogList::updateOrCreate(
                        ['invoice_number' => $invoiceNumber],
                        ['resolved' => false]
                    );
                }
                
                return response()->json([
                    'success' => true,
                    'message' => count($invoiceNumbers) . ' invoice(s) added to backlog list'
                ]);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Invalid data format'
            ], 400);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
