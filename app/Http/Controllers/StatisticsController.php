<?php

namespace App\Http\Controllers;

use App\Models\DailyStockData;
use App\Models\Inbound;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StatisticsController extends Controller
{
    public function index(Request $request)
    {
        //set execution time limit to limitless
        set_time_limit(0);
        if (Auth::user()->role !== 'admin') {
            return redirect()->route('home.index');
        }

        // Default to last 7 days if no dates provided
        $endDate = $request->input('end_date', Carbon::today()->format('Y-m-d'));
        $startDate = $request->input('start_date', Carbon::today()->subDays(6)->format('Y-m-d'));

        $stockStartDate = $request->input('stock_start_date', Carbon::today()->subDays(6)->format('Y-m-d'));
        $stockEndDate = $request->input('stock_end_date', Carbon::today()->format('Y-m-d'));

        $startDateCarbon = Carbon::parse($startDate);
        $endDateCarbon = Carbon::parse($endDate);

        $dailyResults = [];

        for ($date = $startDateCarbon->copy(); $date->lte($endDateCarbon); $date->addDay()) {
            $currentDate = $date->format('Y-m-d');
            $previousDate = $date->copy()->subDay()->format('Y-m-d');

            $currentStock = DailyStockData::whereDate('created_at', $currentDate)->first();
            $previousStock = DailyStockData::whereDate('created_at', $previousDate)->first();
            $inbounds = Inbound::with('products.product')
                ->whereDate('inbound_date', $currentDate)
                ->get();

            $salesData = $this->calculateSales($currentStock, $previousStock, $inbounds);

            $totalSold = collect($salesData)->sum('sold');
            $totalValue = collect($salesData)->sum('value');
            $avgPrice = $totalSold > 0 ? $totalValue / $totalSold : 0;
            $uniqueProducts = collect($salesData)->filter(fn($i) => $i['sold'] > 0)->count();
            $topSoldProducts = collect($salesData)->sortByDesc('sold')->take(5)->values();
            $topValueProducts = collect($salesData)->sortByDesc('value')->take(5)->values();

            $dailyResults[] = [
                'date' => $currentDate,
                'salesData' => $salesData,
                'totalSold' => $totalSold,
                'totalValue' => $totalValue,
                'avgPrice' => $avgPrice,
                'uniqueProducts' => $uniqueProducts,
                'topSoldProducts' => $topSoldProducts, 
                'topValueProducts' => $topValueProducts,
            ];
        }

        // Calculate range-wide aggregations
        $rangeAggregation = $this->calculateRangeAggregation($dailyResults);

        // Calculate stock range aggregations
        $stockRangeAggregation = $this->calculateStockRangeAggregation($stockStartDate, $stockEndDate);

        // Get single day stock data (default to today)
        $stockDate = $request->input('stock_date', Carbon::today()->format('Y-m-d'));
        $stockData = $this->getSingleDayStockData($stockDate);

        return view('statistics.index', [
            'dailyData' => $dailyResults,
            'rangeAggregation' => $rangeAggregation,
            'stockRangeAggregation' => $stockRangeAggregation,
            'stockData' => $stockData,
            'stockDate' => $stockDate,
            'startDate' => $startDateCarbon->format('Y-m-d'),
            'endDate' => $endDateCarbon->format('Y-m-d'),
            'stockStartDate' => $stockStartDate,
            'stockEndDate' => $stockEndDate,
        ]);
    }
    
    private function calculateSales($currentStockData, $previousStockData, $inbounds)
    {
        set_time_limit(0);
        $salesData = [];
        $products = Product::all();
        
        // Create lookup maps
        $currentStockMap = [];
        $previousStockMap = [];
        $inboundMap = [];
        
        // Process current stock data
        if ($currentStockData && $currentStockData->data) {
            $stockItems = json_decode($currentStockData->data, true);
            foreach ($stockItems as $item) {
                $currentStockMap[$item['productNumber']] = $item['stock'];
            }
        }
        
        // Process previous stock data
        if ($previousStockData && $previousStockData->data) {
            $stockItems = json_decode($previousStockData->data, true);
            foreach ($stockItems as $item) {
                $previousStockMap[$item['productNumber']] = $item['stock'];
            }
        }
        foreach ($inbounds as $inbound) {
            foreach ($inbound->products as $inboundProduct) {
                $product = $inboundProduct->product;
                if ($product) {
                    $productData = json_decode($product->product_json, true);
                    $productNumber = $productData[0]['itemNumber'] ?? null;
                    
                    if ($productNumber) {
                        if (!isset($inboundMap[$productNumber])) {
                            $inboundMap[$productNumber] = 0;
                        }
                        $inboundMap[$productNumber] += $inboundProduct->quantity;
                    }
                }
            }
        }
        
        foreach ($products as $product) {
            $productData = json_decode($product->product_json, true);
            $productNumber = $productData[0]['itemNumber'] ?? null;
            
            if ($productNumber) {
                $previousStock = $previousStockMap[$productNumber] ?? 0;
                $currentStock = $currentStockMap[$productNumber] ?? 0;
                $inboundQuantity = $inboundMap[$productNumber] ?? 0;
                $sold = $previousStock - $currentStock + $inboundQuantity;
                
                if ($sold > 0 || $inboundQuantity > 0) {
                    $price = $productData[0]['priceGross'] ?? 0;
                    $value = $sold * $price;
                    $base = $previousStock + $inboundQuantity;
                    $percent = $base > 0 ? ($sold / $base) * 100 : 0;

                    $salesData[] = [
                        'productNumber' => $productNumber,
                        'name' => $productData[0]['description'] ?? '',
                        'previousStock' => $previousStock,
                        'currentStock' => $currentStock,
                        'inbounds' => $inboundQuantity,
                        'sold' => $sold,
                        'price' => $price,
                        'value' => $value,
                        'percentSold' => $percent,
                    ];
                }
            }
        }
        
        return $salesData;
    }

    private function calculateRangeAggregation($dailyResults)
    {
        if (empty($dailyResults)) {
            return [
                'totalSold' => 0,
                'totalValue' => 0,
                'avgDailySales' => 0,
                'avgPrice' => 0,
                'uniqueProducts' => 0,
                'totalDays' => 0,
                'aggregatedSalesData' => [],
                'topSoldProducts' => [],
                'topValueProducts' => [],
                'dailyTrends' => []
            ];
        }

        // Aggregate totals across all days
        $totalSold = 0;
        $totalValue = 0;
        $allProducts = [];
        $dailyTrends = [];

        foreach ($dailyResults as $day) {
            $totalSold += $day['totalSold'];
            $totalValue += $day['totalValue'];

            // Store daily trends for charts
            $dailyTrends[] = [
                'date' => $day['date'],
                'totalSold' => $day['totalSold'],
                'totalValue' => $day['totalValue'],
                'uniqueProducts' => $day['uniqueProducts']
            ];

            // Aggregate product data across all days
            foreach ($day['salesData'] as $product) {
                $productNumber = $product['productNumber'];

                if (!isset($allProducts[$productNumber])) {
                    $allProducts[$productNumber] = [
                        'productNumber' => $productNumber,
                        'name' => $product['name'],
                        'totalSold' => 0,
                        'totalValue' => 0,
                        'avgPrice' => $product['price'],
                        'daysActive' => 0
                    ];
                }

                $allProducts[$productNumber]['totalSold'] += $product['sold'];
                $allProducts[$productNumber]['totalValue'] += $product['value'];
                if ($product['sold'] > 0) {
                    $allProducts[$productNumber]['daysActive']++;
                }
            }
        }

        // Calculate averages and metrics
        $totalDays = count($dailyResults);
        $avgDailySales = $totalDays > 0 ? $totalSold / $totalDays : 0;
        $avgPrice = $totalSold > 0 ? $totalValue / $totalSold : 0;
        $uniqueProducts = count($allProducts);

        // Convert aggregated products to array and sort
        $aggregatedSalesData = array_values($allProducts);

        // Sort by total sold and value for top products
        $topSoldProducts = collect($aggregatedSalesData)
            ->sortByDesc('totalSold')
            ->take(10)
            ->values()
            ->toArray();

        $topValueProducts = collect($aggregatedSalesData)
            ->sortByDesc('totalValue')
            ->take(10)
            ->values()
            ->toArray();

        return [
            'totalSold' => $totalSold,
            'totalValue' => $totalValue,
            'avgDailySales' => $avgDailySales,
            'avgPrice' => $avgPrice,
            'uniqueProducts' => $uniqueProducts,
            'totalDays' => $totalDays,
            'aggregatedSalesData' => $aggregatedSalesData,
            'topSoldProducts' => $topSoldProducts,
            'topValueProducts' => $topValueProducts,
            'dailyTrends' => $dailyTrends
        ];
    }

    private function calculateStockRangeAggregation($stockStartDate, $stockEndDate)
    {
        $startDateCarbon = Carbon::parse($stockStartDate);
        $endDateCarbon = Carbon::parse($stockEndDate);

        // Get starting stock (stock at the beginning of the range)
        $startingStockData = DailyStockData::whereDate('created_at', $startDateCarbon)->first();

        // Get ending stock (stock at the end of the range)
        $endingStockData = DailyStockData::whereDate('created_at', $endDateCarbon)->first();

        // Calculate total products sold during the range (using existing sales logic)
        $totalProductsSold = $this->calculateTotalSoldInRange($startDateCarbon, $endDateCarbon);

        // Calculate total inbound stock during the range
        $totalInboundStock = $this->calculateTotalInboundInRange($startDateCarbon, $endDateCarbon);

        // Process starting stock
        $startingStock = $this->processStockData($startingStockData);

        // Process ending stock
        $endingStock = $this->processStockData($endingStockData);

        return [
            'startingStock' => $startingStock,
            'endingStock' => $endingStock,
            'totalProductsSold' => $totalProductsSold,
            'totalInboundStock' => $totalInboundStock,
            'dateRange' => [
                'start' => $startDateCarbon->format('Y-m-d'),
                'end' => $endDateCarbon->format('Y-m-d'),
                'days' => $startDateCarbon->diffInDays($endDateCarbon) + 1
            ]
        ];
    }

    private function calculateTotalSoldInRange($startDate, $endDate)
    {
        $totalSold = 0;
        $totalValue = 0;
        $productSales = [];

        // Iterate through each day in the range
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $currentDate = $date->format('Y-m-d');
            $previousDate = $date->copy()->subDay()->format('Y-m-d');

            $currentStock = DailyStockData::whereDate('created_at', $currentDate)->first();
            $previousStock = DailyStockData::whereDate('created_at', $previousDate)->first();
            $inbounds = Inbound::with('products.product')
                ->whereDate('inbound_date', $currentDate)
                ->get();

            $salesData = $this->calculateSales($currentStock, $previousStock, $inbounds);

            // Aggregate sales data
            foreach ($salesData as $sale) {
                $totalSold += $sale['sold'];
                $totalValue += $sale['value'];

                $productNumber = $sale['productNumber'];
                if (!isset($productSales[$productNumber])) {
                    $productSales[$productNumber] = [
                        'productNumber' => $productNumber,
                        'name' => $sale['name'],
                        'totalSold' => 0,
                        'totalValue' => 0,
                        'price' => $sale['price']
                    ];
                }

                $productSales[$productNumber]['totalSold'] += $sale['sold'];
                $productSales[$productNumber]['totalValue'] += $sale['value'];
            }
        }

        return [
            'totalQuantity' => $totalSold,
            'totalValue' => $totalValue,
            'productBreakdown' => array_values($productSales)
        ];
    }

    private function calculateTotalInboundInRange($startDate, $endDate)
    {
        $totalInbound = 0;
        $totalValue = 0;
        $productInbounds = [];

        // Get all inbounds within the date range
        $inbounds = Inbound::with('products.product')
            ->whereBetween('inbound_date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->get();

        foreach ($inbounds as $inbound) {
            foreach ($inbound->products as $inboundProduct) {
                $product = $inboundProduct->product;
                if ($product) {
                    $productData = json_decode($product->product_json, true);
                    $productNumber = $productData[0]['itemNumber'] ?? null;
                    $price = $productData[0]['priceGross'] ?? 0;

                    if ($productNumber) {
                        $quantity = $inboundProduct->quantity;
                        $value = $quantity * $price;

                        $totalInbound += $quantity;
                        $totalValue += $value;

                        if (!isset($productInbounds[$productNumber])) {
                            $productInbounds[$productNumber] = [
                                'productNumber' => $productNumber,
                                'name' => $productData[0]['description'] ?? '',
                                'totalInbound' => 0,
                                'totalValue' => 0,
                                'price' => $price
                            ];
                        }

                        $productInbounds[$productNumber]['totalInbound'] += $quantity;
                        $productInbounds[$productNumber]['totalValue'] += $value;
                    }
                }
            }
        }

        return [
            'totalQuantity' => $totalInbound,
            'totalValue' => $totalValue,
            'productBreakdown' => array_values($productInbounds)
        ];
    }

    private function processStockData($stockData)
    {
        if (!$stockData || !$stockData->data) {
            return [
                'totalQuantity' => 0,
                'totalValue' => 0,
                'totalProducts' => 0,
                'productBreakdown' => []
            ];
        }

        $stockItems = json_decode($stockData->data, true);
        $totalQuantity = 0;
        $totalValue = 0;
        $productBreakdown = [];

        // Get all products for additional information
        $products = Product::all();
        $productMap = [];
        foreach ($products as $product) {
            $productData = json_decode($product->product_json, true);
            $productNumber = $productData[0]['itemNumber'] ?? null;
            if ($productNumber) {
                $productMap[$productNumber] = [
                    'name' => $productData[0]['description'] ?? '',
                    'price' => $productData[0]['priceGross'] ?? 0
                ];
            }
        }

        // Process stock items
        foreach ($stockItems as $item) {
            $productNumber = $item['productNumber'];
            $stock = $item['stock'];
            $price = $productMap[$productNumber]['price'] ?? 0;
            $value = $stock * $price;

            $totalQuantity += $stock;
            $totalValue += $value;

            $productBreakdown[] = [
                'productNumber' => $productNumber,
                'name' => $productMap[$productNumber]['name'] ?? 'Unknown Product',
                'stock' => $stock,
                'price' => $price,
                'value' => $value
            ];
        }

        return [
            'totalQuantity' => $totalQuantity,
            'totalValue' => $totalValue,
            'totalProducts' => count($productBreakdown),
            'productBreakdown' => $productBreakdown
        ];
    }

    private function getStockDataForDate($date)
    {
        $dateCarbon = Carbon::parse($date);

        // Get stock data for the selected date
        $currentStockData = DailyStockData::whereDate('created_at', $dateCarbon)->first();

        // Get previous day's stock data for comparison
        $previousDate = $dateCarbon->copy()->subDay();
        $previousStockData = DailyStockData::whereDate('created_at', $previousDate)->first();

        // Debug: Log what we found
        Log::info('Stock Data Query Debug', [
            'requestedDate' => $date,
            'parsedDate' => $dateCarbon->format('Y-m-d'),
            'currentStockFound' => $currentStockData ? true : false,
            'previousStockFound' => $previousStockData ? true : false,
            'currentStockDataSample' => $currentStockData ? substr($currentStockData->data, 0, 200) : null,
            'totalDailyStockRecords' => DailyStockData::count(),
            'recentDates' => DailyStockData::orderBy('created_at', 'desc')->take(5)->pluck('created_at')->toArray()
        ]);

        $stockItems = [];

        if ($currentStockData && $currentStockData->data) {
            $currentStockItems = json_decode($currentStockData->data, true);

            $previousStockMap = [];
            if ($previousStockData && $previousStockData->data) {
                $previousStockItems = json_decode($previousStockData->data, true);
                foreach ($previousStockItems as $item) {
                    $previousStockMap[$item['productNumber']] = $item['stock'];
                }
            }

            // Get all products for additional information
            $products = Product::all();
            $productMap = [];
            foreach ($products as $product) {
                $productData = json_decode($product->product_json, true);
                $productNumber = $productData[0]['itemNumber'] ?? null;
                if ($productNumber) {
                    $productMap[$productNumber] = [
                        'name' => $productData[0]['description'] ?? '',
                        'price' => $productData[0]['priceGross'] ?? 0
                    ];
                }
            }

            // Process current stock items
            foreach ($currentStockItems as $item) {
                $productNumber = $item['productNumber'];
                $currentStock = $item['stock'];
                $previousStock = $previousStockMap[$productNumber] ?? 0;
                $stockChange = $currentStock - $previousStock;

                $stockItems[] = [
                    'productNumber' => $productNumber,
                    'name' => $productMap[$productNumber]['name'] ?? 'Unknown Product',
                    'currentStock' => $currentStock,
                    'previousStock' => $previousStock,
                    'stockChange' => $stockChange,
                    'changeType' => $stockChange > 0 ? 'increase' : ($stockChange < 0 ? 'decrease' : 'no_change'),
                    'price' => $productMap[$productNumber]['price'] ?? 0
                ];
            }
        }

        usort($stockItems, function($a, $b) {
            return strcmp($a['productNumber'], $b['productNumber']);
        });

        $result = [
            'date' => $date,
            'stockItems' => $stockItems,
            'totalProducts' => count($stockItems),
            'totalStockValue' => array_sum(array_map(function($item) {
                return $item['currentStock'] * $item['price'];
            }, $stockItems)),
            'hasData' => !empty($stockItems)
        ];

        // Debug: Log final result
        Log::info('Stock Data Final Result', [
            'date' => $date,
            'hasData' => $result['hasData'],
            'totalProducts' => $result['totalProducts'],
            'stockItemsCount' => count($stockItems),
            'firstFewItems' => array_slice($stockItems, 0, 3)
        ]);

        return $result;
    }

    private function getSingleDayStockData($date)
    {
        $dateCarbon = Carbon::parse($date);

        // Get stock data for the selected date
        $currentStockData = DailyStockData::whereDate('created_at', $dateCarbon)->first();

        // Get previous day's stock data for comparison
        $previousDate = $dateCarbon->copy()->subDay();
        $previousStockData = DailyStockData::whereDate('created_at', $previousDate)->first();

        $stockItems = [];

        if ($currentStockData && $currentStockData->data) {
            $currentStockItems = json_decode($currentStockData->data, true);

            // Create lookup map for previous stock
            $previousStockMap = [];
            if ($previousStockData && $previousStockData->data) {
                $previousStockItems = json_decode($previousStockData->data, true);
                foreach ($previousStockItems as $item) {
                    $previousStockMap[$item['productNumber']] = $item['stock'];
                }
            }

            // Get all products for additional information
            $products = Product::all();
            $productMap = [];
            foreach ($products as $product) {
                $productData = json_decode($product->product_json, true);
                $productNumber = $productData[0]['itemNumber'] ?? null;
                if ($productNumber) {
                    $productMap[$productNumber] = [
                        'name' => $productData[0]['description'] ?? '',
                        'price' => $productData[0]['priceGross'] ?? 0,
                        'cost' => $productData[0]['purchasePrice'] ?? 0
                    ];
                }
            }

            // Process current stock items
            foreach ($currentStockItems as $item) {
                $productNumber = $item['productNumber'];
                $currentStock = $item['stock'];
                $previousStock = $previousStockMap[$productNumber] ?? 0;
                $stockChange = $currentStock - $previousStock;

                $stockItems[] = [
                    'productNumber' => $productNumber,
                    'name' => $productMap[$productNumber]['name'] ?? 'Unknown Product',
                    'currentStock' => $currentStock,
                    'previousStock' => $previousStock,
                    'stockChange' => $stockChange,
                    'changeType' => $stockChange > 0 ? 'increase' : ($stockChange < 0 ? 'decrease' : 'no_change'),
                    'price' => $productMap[$productNumber]['price'] ?? 0,
                    'cost' => $productMap[$productNumber]['cost'] ?? 0
                ];
            }
        }

        // Sort by product number
        usort($stockItems, function($a, $b) {
            return strcmp($a['productNumber'], $b['productNumber']);
        });

        $result = [
            'date' => $date,
            'stockItems' => $stockItems,
            'totalProducts' => count($stockItems),
            'totalStockValue' => array_sum(array_map(function($item) {
                return $item['currentStock'] * $item['price'];
            }, $stockItems)),
            'hasData' => !empty($stockItems)
        ];

        // Debug: Log final result
        Log::info('Single Day Stock Final Result', [
            'date' => $date,
            'hasData' => $result['hasData'],
            'totalProducts' => $result['totalProducts'],
            'stockItemsCount' => count($stockItems),
            'firstFewItems' => array_slice($stockItems, 0, 3)
        ]);

        return $result;
    }

    // Debug method to test stock data directly
    public function debugStock(Request $request)
    {
        $date = $request->input('date', Carbon::today()->format('Y-m-d'));
        $stockData = $this->getStockDataForDate($date);

        return response()->json([
            'debug' => true,
            'requestedDate' => $date,
            'stockData' => $stockData,
            'rawDailyStockData' => DailyStockData::whereDate('created_at', $date)->first()
        ]);
    }
}
