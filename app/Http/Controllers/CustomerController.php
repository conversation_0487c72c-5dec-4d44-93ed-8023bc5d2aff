<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use App\Models\User;
use Illuminate\Support\Facades\Http;

class CustomerController extends Controller
{
    public function index(Request $request)
    {
        $users = User::all();
        $search = $request->query('search');
        $perPage = $request->input('per_page', 50);
        
        // Sadece geçerli sayfa boyutlarına izin ver
        if (!in_array($perPage, [20, 50, 100])) {
            $perPage = 50;
        }

        $query = Customer::orderBy('created_at', 'desc');

        if ($search) {
            $searchLower = strtolower($search);
            $columns = ['first_name', 'last_name', 'email', 'company', 'phone'];

            $query->where(function ($q) use ($columns, $searchLower) {
                foreach ($columns as $column) {
                    $q->orWhereRaw("LOWER($column) LIKE ?", ['%' . $searchLower . '%']);
                }
            });
        }

        $customers = $query->paginate($perPage);

        return view('customer.index', [
            'customers' => $customers,
            'search' => $search,
            'perPage' => $perPage,
            'users' => $users,
        ]);
    }

    public function edit($id)
    {
        $customer = Customer::findOrFail($id);
        return view('customer.edit', ['customer' => $customer]);
    }

    public function update(Request $request, $id)
    {
        try {
            $validated = $request->validate([
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'phone' => 'nullable|string|max:255',
                'company' => 'nullable|string|max:255',
                'whatsapp_number' => 'nullable|string|max:255',
                'notification_email' => 'nullable|email|max:255',
                'address' => 'nullable|string',
                'house_number' => 'nullable|string|max:255',
                'zip_code' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'country' => 'nullable|string|max:255',
                'iban' => 'nullable|string|max:255',
            ]);

            $customer = Customer::findOrFail($id);
            $validated['whatsapp_number'] = preg_replace('/\s+/', '', $validated['whatsapp_number'] ?? '');
            $customer->update($validated);
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $validated = $request->validate([
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'email' => 'nullable|email|max:255',
                'phone' => 'nullable|string|max:255',
                'company' => 'nullable|string|max:255',
                'whatsapp_number' => 'nullable|string|max:255',
                'notification_email' => 'nullable|email|max:255',
                'address' => 'nullable|string',
                'house_number' => 'nullable|string|max:255',
                'zip_code' => 'nullable|string|max:255',
                'city' => 'nullable|string|max:255',
                'country' => 'nullable|string|max:255',
                'iban' => 'nullable|string|max:255',
                'client_id' => 'nullable|string',
            ]);
            
            $validated['whatsapp_number'] = preg_replace('/\s+/', '', $validated['whatsapp_number'] ?? '');
            Customer::create($validated);
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $customer = Customer::findOrFail($id);
            $customer->delete();
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

        public function unsubscribe($customerId) {
        $customer = Customer::where('client_id', $customerId)->first();
        $customer->update(['email_permission' => false]);
        session()->flash('success', 'You have successfully unsubscribed from our emails.');
        return '<html>
                <head>
                    <title>Unsubscribe Successful</title>
                </head>
                <body>
                    <h1>Unsubscribe Successful</h1>
                    <p>You have successfully unsubscribed from our emails.</p>
                </body>
                </html>';
        }
    
    public function receiveWebhook(Request $request)
    {
        if($request->query('token') != env('MSG_VERIFY_TOKEN')) {
            return response()->json(['message' => 'Failed'], 400);
        }

        $payload = (array) $request->all();
        $userName = $payload['meta']['sender']['name'] ?? 'Unknown';
        $phoneNumber = $payload['meta']['sender']['phone_number'];
        $conversation_id = $payload['messages'][0]['conversation_id'];
        $label = $payload['labels'][0] ?? 'No label';
        $messageContent = $payload['messages'][0]['content'] ?? 'No message';
        $accountId = $payload['messages'][0]['account_id'] ?? null;
        $senderId = $payload['messages'][0]['sender_id'] ?? null;
    
        $customer = Customer::where('whatsapp_number', $phoneNumber)->first();
        if(!$customer) return response()->json(['message' => 'Customer not found'], 200);

        $customAttributes = $payload['meta']['sender']['custom_attributes'] ?? [];
        $data = [
            'phone' => $payload['meta']['sender']['phone_number'],
            'name' => $payload['meta']['sender']['name'] ?? 'Unknown',
            'email' => $customAttributes['email'] ?? $payload['meta']['sender']['email'] ?? null,
            'city' => $customAttributes['city'] ?? null,
            'district' => $customAttributes['district'] ?? null,
            'field' => $customAttributes['field'] ?? null,
            'phone2' => $customAttributes['phone2'] ?? null,
        ];
        if (strtolower(trim($messageContent)) === 'keine nachrichten' || strtolower(trim($messageContent)) === 'KEINE NACHRICHTEN') {
            $customer->update([
                'whatsapp_permission' => false,
                'email_permission' => false
            ]);
            return response()->json(['message' => 'Permissions updated'], 200);
        }
    }

    /**
     * Assign a user to a customer
     */
    public function assignUser(Request $request)
    {
        // Sadece admin kullanıcılar bu işlemi yapabilir
        if (auth()->user()->role !== 'admin') {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }
        
        $request->validate([
            'customer_id' => 'required|exists:customers,id',
            'user_id' => 'nullable|exists:users,id'
        ]);
        
        $customer = Customer::findOrFail($request->customer_id);
        $customer->yetki_id = $request->user_id ?: null;
        $customer->save();

        // Log to user_assigns table new row
        \App\Models\UserAssign::create([
            'customer_id' => $customer->id,
            'user_id' => $customer->yetki_id
        ]);
        
        return response()->json(['success' => true]);
    }

    /**
     * Get communication ways from SevDesk API
     */
    public function getCommunicationWays()
    {
        try {
            $token = env('SEVDESK_API_TOKEN');
            
            $response = Http::withHeaders([
                'Authorization' => $token
            ])->get('https://my.sevdesk.de/api/v1/CommunicationWay?limit=500000');
            
            if ($response->successful()) {
                // Sadece EMAIL tipindeki iletişim yollarını filtrele
                $communicationWays = collect($response->json('objects'))
                    ->filter(function ($item) {
                        return $item['type'] === 'EMAIL';
                    })
                    ->values();
                
                return response()->json([
                    'success' => true,
                    'data' => $communicationWays
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch communication ways from SevDesk'
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}