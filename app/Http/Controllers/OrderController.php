<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\CustomerAlternativeAddress;
use Illuminate\Http\Request;
use App\Models\Invoice;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;
use Exception;
use App\Models\InvoiceItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Helpers\FtpHelper;
use App\Models\DailyStockData;
use App\Models\Product;

class OrderController extends Controller
{
    public function search(Request $request)
    {
        $term = $request->get('term');
        
        $customers = Customer::where('first_name', 'LIKE', "%{$term}%")
            ->orWhere('last_name', 'LIKE', "%{$term}%")
            ->orWhere('company', 'LIKE', "%{$term}%")
            ->orWhere('email', 'LIKE', "%{$term}%")
            ->select('id', 'first_name', 'last_name', 'company', 'address', 'iban', 'email')
            ->limit(10)
            ->get();

        return response()->json($customers);
    }

    public function getCustomerAlternativeAddress($customerId)
    {
        try {
            $alternativeAddress = CustomerAlternativeAddress::where('customer_id', $customerId)->first();

            if ($alternativeAddress) {
                return response()->json([
                    'success' => true,
                    'data' => $alternativeAddress
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No alternative address found'
                ]);
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching alternative address: ' . $e->getMessage()
            ], 500);
        }
    }

    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 50); // Varsayılan olarak 50 kayıt
        
        // Sadece geçerli sayfa boyutlarına izin ver
        if (!in_array($perPage, [20, 50, 100])) {
            $perPage = 50;
        }
        
        $invoices = Invoice::with(['customer', 'shipmentPackages'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
        
        return view('order.index', [
            'invoices' => $invoices,
            'perPage' => $perPage
        ]);
    }

    public function updateOrderDetails(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            // find invoice
            $invoice = Invoice::with(['customer', 'items'])->findOrFail($id);

            // Check if using alternative delivery address and store it
            $useAlternativeAddress = $request->has('use_alternative_address');
            if ($useAlternativeAddress) {
                $alternativeAddressData = [
                    'customer_id' => $invoice->customer->id,
                    'first_name' => $request->input('delivery_firstName'),
                    'last_name' => $request->input('delivery_lastName'),
                    'email' => $invoice->customer->email, // Use customer's email
                    'phone' => $request->input('delivery_phone'),
                    'company' => $request->input('delivery_company', ''),
                    'address' => $request->input('delivery_address'),
                    'house_number' => $request->input('delivery_housenumber'),
                    'zip_code' => $request->input('delivery_zipcode'),
                    'city' => $request->input('delivery_city'),
                    'country' => $request->input('delivery_country'),
                ];

                // Update or create alternative address
                CustomerAlternativeAddress::updateOrCreate(
                    ['customer_id' => $invoice->customer->id],
                    $alternativeAddressData
                );
            }

            // Update invoice details (status değiştirmeden)
            $invoice->update([
                'company_name' => $request->input('company_name'),
                'address' => $request->input('address'),
                'iban' => $request->input('iban'),
                'notes' => $request->input('notes'),
            ]);
            
            // Delete existing items
            $invoice->items()->delete();

            // Add new items
            $items = $request->input('items', []);
            foreach ($items as $item) {
                // Calculate amounts
                $quantity = floatval($item['quantity']);
                $price = floatval($item['price']);
                $taxRate = floatval($item['tax_rate']);
                
                $netAmount = $quantity * $price;
                $taxAmount = $netAmount * ($taxRate / 100);
                $grossAmount = $netAmount + $taxAmount;
                // invoice item create
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_name' => $item['product_name'],
                    'item_number' => $item['item_number'] ?? null,
                    'description' => $item['description'] ?? null,
                    'quantity' => $quantity,
                    'price' => $price,
                    'tax_rate' => $taxRate,
                    'gross_amount' => $grossAmount,
                ]);
            }

            // Recalculate invoice totals
            $totals = $invoice->items()
                ->selectRaw('
                    SUM(price * quantity) as total_net,
                    SUM((price * quantity) * (tax_rate / 100)) as total_tax,
                    SUM(gross_amount) as total_gross
                ')
                ->first();

            $invoice->update([
                'total' => $totals->total_gross ?? 0,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order updated successfully'
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Error updating order: ' . $e->getMessage()
            ], 500);
        }
    }

    public function approve(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            
            $invoice = Invoice::with(['customer', 'items'])->findOrFail($id);
            
            // Check if using alternative delivery address
            $useAlternativeAddress = $request->has('use_alternative_address');

            // Store alternative address if provided
            if ($useAlternativeAddress) {
                $alternativeAddressData = [
                    'customer_id' => $invoice->customer->id,
                    'first_name' => $request->input('delivery_firstName'),
                    'last_name' => $request->input('delivery_lastName'),
                    'email' => $invoice->customer->email, // Use customer's email
                    'phone' => $request->input('delivery_phone'),
                    'company' => $request->input('delivery_company', ''),
                    'address' => $request->input('delivery_address'),
                    'house_number' => $request->input('delivery_housenumber'),
                    'zip_code' => $request->input('delivery_zipcode'),
                    'city' => $request->input('delivery_city'),
                    'country' => $request->input('delivery_country'),
                ];

                CustomerAlternativeAddress::updateOrCreate(
                    ['customer_id' => $invoice->customer->id],
                    $alternativeAddressData
                );
            }

            $items = $request->input('items', []);

            $orderEntries = array_map(function ($item, $index) {
                $quantity = floatval($item['quantity']);
                $price = floatval($item['price']);
                $taxRate = floatval($item['tax_rate']);

                $netAmount = $quantity * $price;
                $taxAmount = $netAmount * ($taxRate / 100);
                $grossAmount = $netAmount + $taxAmount;

                return [
                    'orderEntryId' => $index + 1,
                    'productNumber' => $item['item_number'],
                    'count' => $quantity,
                    'vatRate' => intval(['0.00' => '1', '7.00' => '3', '19.00' => '2'][$item['tax_rate']] ?? $item['tax_rate']),
                    'priceNet' => number_format($price, 2),
                    'priceGross' => number_format($grossAmount / $quantity, 2)
                ];
            }, $items, array_keys($items));

            $formattedData = [[
                'billingAddress' => [
                    'customerNumber' => strval($invoice->customer->id),
                    'lastName' => $invoice->customer->last_name,
                    'firstName' => $invoice->customer->first_name,
                    'company' => $invoice->customer->company ?? '',
                    'company2' => '', 
                    'salutation' => '',
                    'address' => $invoice->customer->address,
                    'houseNumber' => $invoice->customer->house_number ?? '', 
                    'addressAddition' => '',
                    'zipCode' => $invoice->customer->zip_code ?? '',
                    'city' => $invoice->customer->city ?? '',
                    'countryIsoCode' => $invoice->customer->countryIsoCode ?? 'DE',
                    'county' => $invoice->customer->country,
                    'vatId' => $invoice->customer->vat_id ?? '',
                    'phone' => $invoice->customer->phone ?? '',
                    'email' => $invoice->customer->email ?? ''
                ],
                'deliveryAddress' => $useAlternativeAddress ? [
                    'customerNumber' => strval($invoice->customer->id),
                    'lastName' => $request->input('delivery_lastName'),
                    'firstName' => $request->input('delivery_firstName'),
                    'company' => $request->input('delivery_company', ''),
                    'company2' => '',
                    'salutation' => '',
                    'address' => $request->input('delivery_address'),
                    'houseNumber' => $request->input('delivery_housenumber'),
                    'addressAddition' => '',
                    'zipCode' => $request->input('delivery_zipcode'),
                    'city' => $request->input('delivery_city'),
                    'countryIsoCode' => 'DE',
                    'county' => $request->input('delivery_country'),
                    'vatId' => $invoice->customer->vat_id ?? '',
                    'phone' => $request->input('delivery_phone'),
                    'email' => $invoice->customer->email ?? ''
                ] : [
                    'customerNumber' => strval($invoice->customer->id),
                    'lastName' => $invoice->customer->last_name,
                    'firstName' => $invoice->customer->first_name,
                    'company' => $invoice->customer->company ?? '',
                    'company2' => '',
                    'salutation' => '',
                    'address' => $invoice->customer->address,
                    'houseNumber' => $invoice->customer->house_number ?? '',
                    'addressAddition' => '',
                    'zipCode' => $invoice->customer->zip_code ?? '',
                    'city' => $invoice->customer->city ?? '',
                    'countryIsoCode' => $invoice->customer->countryIsoCode ?? 'DE',
                    'county' => $invoice->customer->country,
                    'vatId' => $invoice->customer->vat_id ?? '',
                    'phone' => $invoice->customer->phone ?? '',
                    'email' => $invoice->customer->email ?? ''
                ],
                'order' => [
                    'orderNr' => $invoice->invoice_number,
                    'orderDate' => is_string($invoice->invoice_date) 
                        ? $invoice->invoice_date 
                        : $invoice->invoice_date->format('Y-m-d'),
                    'user' => $invoice->customer->first_name . ' ' . $invoice->customer->last_name,
                    'userEmail' => $invoice->customer->email ?? '',
                    'paymentDetails' => $invoice->iban ? 'B2B' : 'Other',
                    'carrier' => 'DHL'
                ],
                'orderEntries' => $orderEntries,
            ]];
            // API'ye JSON verisini gönder
            $response = Http::withHeaders([
                'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
                'Content-Type' => 'application/json',
                'Accept' => '*/*',
                'Connection' => 'keep-alive',
                'User-Agent' => 'PostmanRuntime/7.29.0',
                'Host' => 'rest-api.portica.de',
                'Accept-Encoding' => 'gzip, deflate, br'
            ])->post('https://rest-api.portica.de/nurederm/api/order', $formattedData);
            
            // API yanıtını logla
            Log::info('Portica API Response', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            if($response->status() > 299) {
                Log::info('Portica API SENT DATA', [
                    'status' => $response->status(),
                    'sent_data' => $formattedData
                ]);
            }

            $pdfFilename = $invoice->invoice_number . '.pdf';
            FtpHelper::uploadInvoicePdf($pdfFilename);
            
            // Update invoice details
            $invoice->update([
                'status' => 1, // Approve the order
                'company_name' => $request->input('company_name'),
                'address' => $request->input('address'),
                'iban' => $request->input('iban'),
                'notes' => $request->input('notes'),
                'portica_json' => json_encode($formattedData),
                'portica_response' => $response->body() ?? null,
                'approved_time' => now() // Şu anki tarih ve saati ekle
            ]);
            
            // Delete existing items
            $invoice->items()->delete();

            // Add new items
            $items = $request->input('items', []);
            foreach ($items as $item) {
                // Calculate amounts
                $quantity = floatval($item['quantity']);
                $price = floatval($item['price']);
                $taxRate = floatval($item['tax_rate']);
                
                $netAmount = $quantity * $price;
                $taxAmount = $netAmount * ($taxRate / 100);
                $grossAmount = $netAmount + $taxAmount;

                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_name' => $item['product_name'],
                    'item_number' => $item['item_number'] ?? null,
                    'description' => $item['description'] ?? null,
                    'quantity' => $quantity,
                    'price' => $price,
                    'tax_rate' => $taxRate,
                    'gross_amount' => $grossAmount,
                ]);
            }

            // Recalculate invoice totals
            $totals = $invoice->items()
                ->selectRaw('
                    SUM(price * quantity) as total_net,
                    SUM((price * quantity) * (tax_rate / 100)) as total_tax,
                    SUM(gross_amount) as total_gross
                ')
                ->first();

            $invoice->update([
                'total' => $totals->total_gross ?? 0,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order approved successfully',
                //'api_response' => $response->json()
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Error approving order: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Error approving order: ' . $e->getMessage()
            ], 500);
        }
    }

    public function reset($id)
    {
        try {
            DB::beginTransaction();
            
            $invoice = Invoice::findOrFail($id);
            $invoice->update([
                'status' => 0, // Reset to pending
                'portica_json' => null,
                'portica_response' => null,
                'approved_time' => null
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order reset successfully'
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Error resetting order: ' . $e->getMessage()
            ], 500);
        }
    }
    public function cancelOrder($id){
        $invoice = Invoice::findOrFail($id);

        $url = "https://rest-api.portica.de/nurederm/api/order/{$invoice->invoice_number}";
        Log::info('URL: ' . $url);
        Log::info('Invoice Number: ' . $invoice->invoice_number);

         $response = Http::withHeaders([
                    'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
                    'Content-Type' => 'application/json',
                    'Accept' => '*/*',
                    'Accept-Encoding' => 'gzip, deflate, br',
                    'Connection' => 'keep-alive',
                    'User-Agent' => 'PostmanRuntime/7.29.0',
                    'Host' => 'rest-api.portica.de'
                ])->get($url);
        Log::info('Response Status: ' . $response->status());
        Log::info('Response Body: ' . $response->body());
        
        $data = $response->json('data');

        if ($data) {
            $hasValidPackage = collect($data['packages'])->contains(function ($pkg) {
                return !is_null($pkg['carrier']);
            });

            if ($hasValidPackage) {
                return response()->json([
                    'success' => false,
                    'message' => 'Shipment has already been processed. Cannot cancel order.'
                ], 400);
            }
        }
         try {
            DB::beginTransaction();
            
            $invoice->update([
                'status' => 2, 
                'portica_json' => null,
                'portica_response' => null,
                'approved_time' => null
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully'
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Error cancelling order: ' . $e->getMessage()
            ], 500);
        }
    }
    public function fetchStockData($token)
    {
        if ($token !== '066d961f862765db6ec0ba2de97c00cf') {
            return;
        }
        $url = 'https://rest-api.portica.de/nurederm/api/stock';
        $response = Http::withHeaders([
            'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
            'Content-Type' => 'application/json',
            'Accept' => '*/*',
            'Accept-Encoding' => 'gzip, deflate, br',
            'Connection' => 'keep-alive',
            'User-Agent' => 'PostmanRuntime/7.29.0',
            'Host' => 'rest-api.portica.de'
        ])->get($url);

        $stock_data = $response->json('data', []);
        $products = Product::all();
        $productNumbers = [];
        
        foreach ($products as $product) {
            $decodedArray = json_decode($product->product_json, true);

            if (is_array($decodedArray) && isset($decodedArray[0]['itemNumber'])) {
                $productNumber = strtoupper(trim($decodedArray[0]['itemNumber']));
                $productNumbers[] = $productNumber;
            } else {
                Log::warning('product_json hatalı formatta: ID ' . $product->id);
            }
        }

        $productNumbers = array_unique($productNumbers);
        $daily_stock_data = [];

        foreach ($productNumbers as $productNumber) {
            $stockItem = collect($stock_data)->first(function ($item) use ($productNumber) {
                return strtoupper(trim($item['productNumber'])) === $productNumber;
            });

            if ($stockItem) {
                $stock_quantity = $stockItem['stock'] ?? 0;
                $daily_stock_data[] = [
                    'productNumber' => $productNumber,
                    'stock' => $stock_quantity,
                ];
            } else {
                $daily_stock_data[] = ['productNumber' => $productNumber, 'stock' => 0];
            }
        }

        if (!empty($daily_stock_data)) {
            DailyStockData::create([
                'data' => json_encode($daily_stock_data),
            ]);
        }
    }

}







