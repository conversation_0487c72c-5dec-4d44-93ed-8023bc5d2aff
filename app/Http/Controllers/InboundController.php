<?php

namespace App\Http\Controllers;

use App\Enums\InboundStatus;
use App\Enums\InboundType;
use App\Models\Inbound;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class InboundController extends Controller
{
    public function index(){
        if(Auth::user()->role !== 'admin') {
            return redirect()->route('home.index');
        }
        $inbounds = Inbound::with('products')->get();
        return view('inbound.index', compact('inbounds'));
    }

    public function create()
    {
        if(Auth::user()->role !== 'admin') {
            return redirect()->route('home.index');
        }
        
        $products = Product::all();
        return view('inbound.create', compact('products'));
    }

    public function findProducts(Request $request)
    {
        $productNumbers = $request->json('productNumbers', []);
        
        $products = [];
        $notFound = [];
        
        foreach ($productNumbers as $itemNumber) {
            Log::info('Searching for product with item number: ' . $itemNumber);
            $product = Product::whereRaw('JSON_EXTRACT(product_json, "$[0].itemNumber") = ?', [$itemNumber])
                ->first();
            
            if ($product) {
                $productJson = json_decode($product->product_json, true);
                $products[] = [
                    'id' => $product->id,
                    'itemNumber' => $productJson[0]['itemNumber'],
                    'name' => $productJson[0]['description'] ?? 'Unknown'
                ];
            } else {
                $notFound[] = $itemNumber;
            }
        }
        
        return response()->json([
            'success' => true,
            'products' => $products,
            'notFound' => $notFound
        ]);
    }

    public function store(Request $request)
    {
        try {
            $data = $request->json()->all();
            
            $productNumbers = [];
            foreach ($data['items'] as $item) {
                if (!empty($item['productNumber'])) {
                    $productNumbers[] = $item['productNumber'];
                }
            }
            $foundProducts = Product::where(function($query) use ($productNumbers) {
                foreach($productNumbers as $productNumber) {
                    $query->orWhereRaw('JSON_EXTRACT(product_json, "$[0].itemNumber") = ?', [$productNumber]);
                }
            })
                ->get()
                ->keyBy(function ($product) {
                    $productJson = json_decode($product->product_json, true);
                    return $productJson[0]['itemNumber'] ?? '';
                });
            
            $notFoundProducts = [];
            foreach ($productNumbers as $productNumber) {
                if (!$foundProducts->has($productNumber)) {
                    $notFoundProducts[] = $productNumber;
                }
            }
            
            if (!empty($notFoundProducts)) {
                return response()->json([
                    'success' => false,
                    'message' => 'The following product numbers were not found: ' . implode(', ', $notFoundProducts)
                ], 400);
            }
            
            DB::beginTransaction();
            
            $inbound = Inbound::create([
                'inbound_date' => $data['inbound_date'],
                'status' => InboundStatus::PENDING->value,
                'type' => $data['type'] ?? InboundType::NEW_STOCK->value
            ]);
            
            foreach ($data['items'] as $item) {
                $productNumber = $item['productNumber'];
                $product = $foundProducts[$productNumber];
                
                $inbound->products()->create([
                    'product_id' => $product->id,
                    'quantity' => $item['quantity'],
                ]);
            }
            
            DB::commit();
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function edit($id)
    {
        if(Auth::user()->role !== 'admin') {
            return redirect()->route('home.index');
        }
        
        $inbound = Inbound::with(['products.product'])->findOrFail($id);
        return view('inbound.edit', compact('inbound'));
    }

    public function update(Request $request, $id)
    {
        try {
            $inbound = Inbound::findOrFail($id);
            $data = $request->json()->all();
            
            if (isset($data['status']) && count($data) === 1) {
                $inbound->status = $data['status'];
                $inbound->save();
                
                return response()->json(['success' => true]);
            }
            
            if (isset($data['items'])) {
                $productNumbers = [];
                foreach ($data['items'] as $item) {
                    if (!empty($item['productNumber'])) {
                        $productNumbers[] = $item['productNumber'];
                    }
                }
                
                $foundProducts = Product::where(function($query) use ($productNumbers) {
                    foreach($productNumbers as $productNumber) {
                        $query->orWhereRaw('JSON_EXTRACT(product_json, "$[0].itemNumber") = ?', [$productNumber]);
                    }
                })
                    ->get()
                    ->keyBy(function ($product) {
                        $productJson = json_decode($product->product_json, true);
                        return $productJson[0]['itemNumber'] ?? '';
                    });
                
                $notFoundProducts = [];
                foreach ($productNumbers as $productNumber) {
                    if (!$foundProducts->has($productNumber)) {
                        $notFoundProducts[] = $productNumber;
                    }
                }
                
                if (!empty($notFoundProducts)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'The following product numbers were not found: ' . implode(', ', $notFoundProducts)
                    ], 400);
                }
                
                DB::beginTransaction();
                
                // Update inbound date
                if (isset($data['inbound_date'])) {
                    $inbound->inbound_date = $data['inbound_date'];
                    $inbound->save();
                }
                
                $inbound->products()->delete();
                
                foreach ($data['items'] as $item) {
                    $productNumber = $item['productNumber'];
                    $product = $foundProducts[$productNumber];
                    
                    $inbound->products()->create([
                        'product_id' => $product->id,
                        'quantity' => $item['quantity'],
                    ]);
                }
                
                DB::commit();
            }
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            if(Auth::user()->role !== 'admin') {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }
            
            $inbound = Inbound::findOrFail($id);
            
            DB::beginTransaction();
            
            $inbound->products()->delete();
            
            $inbound->delete();
            
            DB::commit();
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    public function checkReturnedProducts($token){
        if($token !== '066d961f862765db6ec0ba2de97c00cf') {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid token'
            ], 401);
        }

        $sevdesk_token = env('SEVDESK_API_TOKEN');
        $today = strtotime(date('Y-m-d 00:00:00'));

        try {
            $response = Http::withHeaders([
                'Authorization' => $sevdesk_token,
            ])->get('https://my.sevdesk.de/api/v1/Invoice', [
                'invoiceType' => 'SR' 
            ]);

            if (!$response->successful()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'API error: ' . $response->body()
                ], 500);
            }

            $invoices = collect($response['objects'])->filter(function ($invoice) use ($today) {
                return strtotime($invoice['update']) >= $today;
            });

            $processedInvoices = [];
            $createdInbounds = [];
            $errors = [];

            foreach ($invoices as $invoice) {
                $parts_url = 'https://my.sevdesk.de/api/v1/InvoicePos?invoice[id]=' . $invoice['id'] . '&invoice[objectName]=Invoice';
                
                $partsResponse = Http::withHeaders([
                    'Authorization' => $sevdesk_token,
                ])->get($parts_url);

                if (!$partsResponse->successful()) {
                    $errors[] = [
                        'invoice_id' => $invoice['id'],
                        'error' => 'Failed to fetch invoice items: ' . $partsResponse->body()
                    ];
                    continue;
                }

                $invoiceItems = $partsResponse['objects'] ?? [];


                if (empty($invoiceItems)) {
                    continue; 
                }

                $productItems = [];
                
                foreach ($invoiceItems as $item) {
                    if (empty($item['name'])) {
                        continue;
                    }

                    $normalizedInput = preg_replace('/\s+/', ' ', trim($item['name']));

                    $product = Product::get()->first(function ($p) use ($normalizedInput) {
                        $json = json_decode($p->product_json, true);
                        $description = $json[0]['description'] ?? null;

                        if (!$description) return false;

                        $normalizedDescription = preg_replace('/\s+/', ' ', trim($description));

                        return $normalizedDescription === $normalizedInput;
                    });

                    
                    if (!$product) {
                        $errors[] = [
                            'invoice_id' => $invoice['id'],
                            'item_name' => $item['name'],
                            'error' => 'Could not extract product code from item name'
                        ];
                        continue;
                    }

                    $productCode = $product->product_json ? json_decode($product->product_json, true)[0]['itemNumber'] ?? null : null;
                    $quantity = abs($item['quantity']); 
                    
                    $productItems[] = [
                        'productCode' => $productCode,
                        'quantity' => $quantity,
                        'name' => $item['name']
                    ];
                }

                if (empty($productItems)) {
                    continue;
                }

                try {
                    DB::beginTransaction();
                    
                    $inbound = Inbound::create([
                        'inbound_date' => now(),
                        'invoice_number' => $invoice['id'],
                        'status' => InboundStatus::PENDING->value,
                        'type' => InboundType::RETURN->value
                    ]);
                    
                    $addedProducts = [];
                    $notFoundProducts = [];
                    
                    foreach ($productItems as $item) {
                        $product = Product::whereRaw('JSON_EXTRACT(product_json, "$[0].itemNumber") = ?', [$item['productCode']])
                            ->first();
                        
                        if ($product) {
                            $inbound->products()->create([
                                'product_id' => $product->id,
                                'quantity' => $item['quantity']
                            ]);
                            
                            $addedProducts[] = [
                                'code' => $item['productCode'],
                                'name' => $item['name'],
                                'quantity' => $item['quantity']
                            ];
                        } else {
                            $notFoundProducts[] = [
                                'code' => $item['productCode'],
                                'name' => $item['name']
                            ];
                        }
                    }
                    
                    if (empty($addedProducts)) {
                        $inbound->delete();
                        $errors[] = [
                            'invoice_id' => $invoice['id'],
                            'error' => 'No matching products found in database',
                            'not_found' => $notFoundProducts
                        ];
                        DB::rollBack();
                        continue;
                    }
                    
                    DB::commit();
                    
                    $createdInbounds[] = [
                        'inbound_id' => $inbound->id,
                        'invoice_id' => $invoice['id'],
                        'products' => $addedProducts,
                        'not_found' => $notFoundProducts
                    ];
                    
                    $processedInvoices[] = $invoice['id'];
                    
                } catch (\Exception $e) {
                    DB::rollBack();
                    $errors[] = [
                        'invoice_id' => $invoice['id'],
                        'error' => 'Failed to create inbound: ' . $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'status' => 'success',
                'processed_invoices_count' => count($processedInvoices),
                'created_inbounds_count' => count($createdInbounds),
                'created_inbounds' => $createdInbounds,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Exception: ' . $e->getMessage()
            ], 500);
        }
    }
}