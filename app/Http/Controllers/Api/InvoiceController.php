<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Product;
use App\Services\Notification\NotificationService;
use App\Services\Whatsapp\NotificationType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class InvoiceController extends Controller
{
     protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function create(Request $request)
    {
        try {
            DB::beginTransaction();

            // Find or create customer
            $customer = Customer::firstOrCreate(
                ['client_id' => $request->input('customer.id')],
                [
                    'first_name' => $request->input('customer.first_name'),
                    'last_name' => $request->input('customer.last_name'),
                    'company' => $request->input('customer.company'),
                    'address' => $request->input('address'),
                    'iban' => $request->input('iban')
                ]
            );
            Log::info('Customer found or created', [
                'customer_id' => $customer->id,
                'client_id' => $request->input('customer.id'),
                'company' => $customer->company
            ]);

            // zip kodunu ve şehri al
            if ($customer->wasRecentlyCreated) {
                // zip kodunu ve şehri al
                $zipCode = $customer->zipCodeAttribute();
                $city = $customer->cityAttribute();
                $country = $customer->countryAttribute();

                // Eğer adres içinden bu bilgileri çıkarmak istiyorsan
                $customer->address = str_replace($zipCode . ' ' . $city . ', ' . $country, '', $customer->address);

                $customer->update([
                    'zip_code' => $zipCode,
                    'city' => $city,
                    'country' => $country,
                    'address' => trim($customer->address)
                ]);
            }

            // Parse and format the date
            $invoiceDate = Carbon::parse($request->input('invoice_date'))->format('Y-m-d H:i:s');
            if ($request->has('pdf')) {
                try {
                    $pdfContent = $request->input('pdf');
                    $fileName = $request->input('invoice_number') . '.pdf';
                    $invoicesDir = public_path('invoices');
                    
                    // Klasör yoksa oluştur
                    if (!file_exists($invoicesDir)) {
                        mkdir($invoicesDir, 0755, true);
                    }
                    
                    $filePath = $invoicesDir . '/' . $fileName;
                    file_put_contents($filePath, base64_decode($pdfContent));
                } catch (\Exception $e) {
                    \Log::error('PDF kaydetme hatası: ' . $e->getMessage());
                    // PDF kaydedilemese bile işleme devam et
                }
            }
            // Create invoice
            $invoice = Invoice::create([
                'invoice_number' => $request->input('invoice_number'),
                'invoice_date' => $invoiceDate,
                'customer_id' => $customer->id,
                'company_name' => $customer->company,
                'address' => $request->input('address'),
                'iban' => $request->input('iban'),
                'total' => $request->input('total_amount'),
                'status' => Invoice::STATUS_PENDING
            ]);

            // Create invoice items
            foreach ($request->input('invoice_items') as $item) {
                // Kullanıcıdan gelen ürünü normalize et
                $normalizedInput = preg_replace('/\s+/', ' ', trim($item['name']));

                // Tüm ürünleri çek, sonra normalize edip karşılaştır
                $product = Product::get()->first(function ($p) use ($normalizedInput) {
                    $json = json_decode($p->product_json, true);
                    $description = $json[0]['description'] ?? null;

                    if (!$description) return false;

                    $normalizedDescription = preg_replace('/\s+/', ' ', trim($description));

                    return $normalizedDescription === $normalizedInput;
                });

                $itemNumber = $product ? json_decode($product->product_json, true)[0]['itemNumber'] ?? null : null;
                
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_name' => $item['name'],
                    'quantity' => $item['quantity'],
                    'description' => $item['description'],
                    'price' => $item['price'],
                    'tax_rate' => $item['tax_rate'],
                    'gross_amount' => $item['total_gross'],
                    // 'part_id' => $item['part_id'],
                    'item_number' => $itemNumber
                ]);
            }

            DB::commit();

            $this->notificationService->notifyUser($invoice, NotificationType::INVOICE_CREATED, [
                'invoice_number' => $invoice->invoice_number,
                'customer_id' => $invoice->customer_id,
            ]);

            return response()->json([
                'message' => 'Invoice created successfully',
                'invoice_id' => $invoice->id
            ], 201);

        } catch (\Exception $e) {
            Log::error('Invoice creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            DB::rollBack();
            return response()->json([
                'error' => 'Failed to create invoice',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}








