<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Customer;
use Illuminate\Support\Facades\Validator;
use Exception;
use App\Helpers\FtpHelper;
use Illuminate\Support\Facades\Http;

class InvoiceController extends Controller
{
   public function index(Request $request)
    {
        $search = $request->query('search');
        $perPage = $request->input('per_page', 50);
        
        // Sadece geçerli sayfa boyutlarına izin ver
        if (!in_array($perPage, [20, 50, 100])) {
            $perPage = 50;
        }

        $query = Invoice::with(['customer', 'items'])
            ->orderBy('created_at', 'desc');

        if ($search) {
            $searchLower = strtolower($search);

            $query->where(function ($q) use ($searchLower) {
                $q->whereRaw('LOWER(portica_json) LIKE ?', ['%' . $searchLower . '%'])
                ->orWhereHas('customer', function ($customerQuery) use ($searchLower) {
                    $customerQuery->whereRaw('LOWER(first_name) LIKE ?', ['%' . $searchLower . '%'])
                                    ->orWhereRaw('LOWER(last_name) LIKE ?', ['%' . $searchLower . '%'])
                                    ->orWhereRaw('LOWER(company) LIKE ?', ['%' . $searchLower . '%']);
                });
            });
        }

        $invoices = $query->paginate($perPage);
        
        return view('order.index', [
            'invoices' => $invoices,
            'perPage' => $perPage,
            'search' => $search
        ]);
    }

    public function show($id)
    {
        $invoice = Invoice::with(['customer', 'items'])
            ->findOrFail($id);

        $subtotal = $invoice->items->sum('gross_amount');
        $sumDiscountGross = $subtotal - $invoice->total;
        if($invoice->portica_json) {
            $deliveryData = json_decode($invoice->portica_json, true);
        }
        
        return view('order.details', [
            'invoice' => $invoice,
            'deliveryData' => $deliveryData ?? null,
            'items' => $invoice->items,
            'customer' => $invoice->customer,
            'subtotal' => $subtotal,
            'sumDiscountGross' => $sumDiscountGross,
            'tax_total' => $invoice->items->sum(function($item) {
                return ($item->price * $item->quantity) * ($item->tax_rate / 100);
            })
        ]);
    }

    public function search(Request $request)
    {
        $term = $request->get('term');
        $customers = Customer::where(function($query) use ($term) {
            $query->where('first_name', 'LIKE', "%{$term}%")
                ->orWhere('last_name', 'LIKE', "%{$term}%")
                ->orWhere('company', 'LIKE', "%{$term}%");
        })
        ->select('id', 'first_name', 'last_name', 'company', 'address', 'iban')
        ->limit(10)
        ->get();
            
        return response()->json($customers);
    }
    
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();
            
            $invoice = Invoice::create([
                'customer_id' => $request->customer_id,
                'invoice_number' => $this->generateInvoiceNumber(),
                'invoice_date' => $request->invoice_date,
                'total' => $request->total,
                'address' => $request->address,
                'iban' => $request->iban
            ]);

            foreach ($request->items as $item) {
                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_name' => $item['product_name'],
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'tax_rate' => $item['tax_rate'],
                    'description' => $item['description'],
                    'gross_amount' => $item['gross_amount']
                ]);
            }

            DB::commit();
            
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function generateInvoiceNumber()
    {
        $lastInvoice = Invoice::orderBy('id', 'desc')->first();
        $nextId = $lastInvoice ? $lastInvoice->id + 1 : 1;
        return 'INV-' . str_pad($nextId, 6, '0', STR_PAD_LEFT);
    }

    public function updateStatus(Request $request, $id)
    {
        try {
            $invoice = Invoice::with(['customer', 'items'])->findOrFail($id);
            
            if ($request->status == Invoice::STATUS_PENDING) {
                // Format data for external system
                $formattedData = [[
                    'billingAddress' => [
                        'customerNumber' => strval($invoice->customer->id),
                        'lastName' => $invoice->customer->last_name,
                        'firstName' => $invoice->customer->first_name,
                        'company' => $invoice->customer->company ?? '',
                        'company2' => '', // If you have this field
                        'salutation' => '', // Default or from customer data if available
                        'address' => $invoice->customer->address,
                        'houseNumber' => $invoice->customer->house_number ?? '', 
                        'addressAddition' => '', // If you have this field
                        'zipCode' => $invoice->customer->zip_code ?? '',
                        'city' => $invoice->customer->city ?? '',
                        'countryIsoCode' => $invoice->customer->countryIsoCode ?? 'DE',
                        'county' => $invoice->customer->country,
                        'vatId' => $invoice->customer->vat_id ?? '',
                        'phone' => $invoice->customer->phone ?? '',
                        'email' => $invoice->customer->email ?? ''
                    ],
                    'deliveryAddress' => [
                        'customerNumber' => strval($invoice->customer->id),
                        'lastName' => $invoice->customer->last_name,
                        'firstName' => $invoice->customer->first_name,
                        'company' => $invoice->customer->company ?? '',
                        'company2' => '', // If you have this field
                        'salutation' => '', // Default or from customer data if available
                        'address' => $invoice->customer->address,
                        'houseNumber' => $invoice->customer->house_number ?? '', 
                        'addressAddition' => '', // If you have this field
                        'zipCode' => $invoice->customer->zip_code ?? '',
                        'city' => $invoice->customer->city ?? '',
                        'countryIsoCode' => $invoice->customer->countryIsoCode ?? 'DE',
                        'county' => $invoice->customer->country,
                        'vatId' => $invoice->customer->vat_id ?? '',
                        'phone' => $invoice->customer->phone ?? '',
                        'email' => $invoice->customer->email ?? ''
                    ],
                    'order' => [
                        'orderNr' => $invoice->invoice_number,
                        'orderDate' => $invoice->invoice_date->format('Y-m-d'),
                        'user' => $invoice->customer->first_name . $invoice->customer->last_name,
                        'userEmail' => $invoice->customer->email ?? '',
                        'paymentDetails' => $invoice->iban ? 'B2B' : 'Other',
                        'carrier' => 'DHL' // Default or from invoice data if available
                    ],
                    'orderEntries' => $invoice->items->map(function ($item, $index) {
                        return [
                            'orderEntryId' => $index + 1,
                            'productNumber' => $item->item_number,
                            'count' => $item->quantity,
                            'vatRate' => intval($item->tax_rate),
                            'priceNet' => number_format($item->price, 2),
                            'priceGross' => number_format($item->gross_amount / $item->quantity, 2)
                        ];
                    })->toArray()
                ]];

                // Make sure portica_json is in the fillable array in the Invoice model
                $invoice->status = $request->status;
                $invoice->portica_json = "1";
                $result = $invoice->save();
                
                // Add debugging to verify save operation
                \Log::info('Invoice update result: ' . ($result ? 'success' : 'failed'));
                \Log::info('Invoice JSON data: ' . $invoice->portica_json);

                return response()->json([
                    'success' => true,
                    'formattedData' => $formattedData,
                    'saved' => $result
                ]);
            }

            // If not approved, just update status
            $invoice->portica_json = "1";
            $invoice->status = $request->status;
            $invoice->save();

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            \Log::error('Error updating invoice status: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            DB::beginTransaction();

            $invoice = Invoice::findOrFail($id);

            // Update invoice details - removed customer_id and invoice_date
            $invoice->update([
                'due_date' => $request->input('due_date'),
                'notes' => $request->input('notes'),
                'status' => $request->input('status', $invoice->status),
                'use_alternative_address' => $request->boolean('use_alternative_address', false),
            ]);

            // Delete existing items
            $invoice->items()->delete();

            // Add new items
            $items = $request->input('items', []);
            foreach ($items as $item) {
                // Calculate amounts
                $quantity = floatval($item['quantity']);
                $price = floatval($item['price']);
                $taxRate = floatval($item['tax_rate']);
                
                $netAmount = $quantity * $price;
                $taxAmount = $netAmount * ($taxRate / 100);
                $grossAmount = $netAmount + $taxAmount;

                InvoiceItem::create([
                    'invoice_id' => $invoice->id,
                    'product_name' => $item['product_name'],
                    'item_number' => $item['item_number'] ?? null,
                    'description' => $item['description'] ?? null,
                    'quantity' => $quantity,
                    'price' => $price,
                    'tax_rate' => $taxRate,
                    'gross_amount' => $grossAmount,
                ]);
            }

            // Recalculate invoice totals
            $totals = $invoice->items()
                ->selectRaw('
                    SUM(price * quantity) as total_net,
                    SUM((price * quantity) * (tax_rate / 100)) as total_tax,
                    SUM(gross_amount) as total_gross
                ')
                ->first();

            $invoice->update([
                'total' => $totals->total_gross ?? 0,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order updated successfully'
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Error updating order: ' . $e->getMessage()
            ], 500);
        }
    }

    public function oldInvoices(Request $request) {
        $sevdesk_token = env('SEVDESK_API_TOKEN');
        $user = Customer::where('id', Invoice::where('invoice_number', $request->invoice_number)->first()->customer_id)->first(); 
        
            $response = Http::withHeaders([
                'Authorization' => $sevdesk_token,
            ])->get('https://my.sevdesk.de/api/v1/Invoice', [
                'contact[id]' => $user->client_id,
                'contact[objectName]' => 'Contact'
            ]);

            $invoices = [];
            foreach ($response->json()['objects'] as $invoice) {
                if($invoice['invoiceNumber'] == $request->invoice_number || $invoice['status'] == '1000' || $invoice['status'] == '50' || $invoice['status'] == '100') {
                    null;
                } else {
                    $invoices[] = $invoice;
                }
            }

            if (empty($invoices)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No overdue invoices found for this customer.'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'invoices' => $invoices
            ]);

    }
}

