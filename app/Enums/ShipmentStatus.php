<?php

namespace App\Enums;

enum ShipmentStatus: string
{
    case DELIVERED_TO_AGENCY = 'ZF';
    case DELIVERED_SUCCESSFULLY = 'ZU';

    public function getMessage(): string
    {
        return match ($this) {
            self::DELIVERED_TO_AGENCY => <<<TEXT
Sehr geehrter Kunde,

Ihre Bestellung wurde erfolgreich zugestellt.
Wir hoffen, dass Sie mit Ihrem Einkauf zufrieden sind!

Vielen Dank für Ihr Vertrauen.

Mit freundlichen Grüßen,
Nurederm Deutschland
TEXT,

            self::DELIVERED_SUCCESSFULLY => <<<TEXT
Sehr geehrter Kunde,

Ihre Bestellung wurde erfolgreich an die Postfiliale / Packstation zugestellt.
Sie können Ihre Sendung dort abholen.

Bitte denken Si<PERSON> daran, Ihre Abholbenachrichtigung und einen gültigen Ausweis mitzubringen.

Vielen Dank für Ihr Vertrauen.

Mit freundlichen Grüßen,
Nurederm Deutschland
TEXT,

            default => '',
        };
    }
}
