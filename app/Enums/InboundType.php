<?php

namespace App\Enums;

enum InboundType: string
{
    case NEW_STOCK = 'new_stock';
    case RETURN = 'return';

    public function label(): string
    {
        return match ($this) {
            self::NEW_STOCK => 'New Stock',
            self::RETURN => 'Return',
        };
    }

    public function badgeClass(): string
    {
        return match ($this) {
            self::NEW_STOCK => 'badge bg-primary-subtle text-primary',
            self::RETURN => 'badge bg-info-subtle text-info',
        };
    }

    public function iconClass(): string
    {
        return match ($this) {
            self::NEW_STOCK => 'fas fa-box me-1',
            self::RETURN => 'fas fa-undo me-1',
        };
    }
}
