<?php

namespace App\Enums;

enum InboundStatus: int
{
    case PENDING = 0;
    case APPROVED = 1;

    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Pending',
            self::APPROVED => 'Approved',
        };
    }

    public function badgeClass(): string
    {
        return match ($this) {
            self::PENDING => 'badge bg-warning-subtle text-warning',
            self::APPROVED => 'badge bg-success-subtle text-success',
        };
    }

    public function iconClass(): string
    {
        return match ($this) {
            self::PENDING => 'fas fa-clock me-1',
            self::APPROVED => 'fas fa-check me-1',
        };
    }
}
