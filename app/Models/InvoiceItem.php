<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InvoiceItem extends Model
{
    protected $table = 'invoice_items';

    protected $fillable = [
        'invoice_id',
        'product_name',
        'item_number',
        'quantity',
        'description',
        'price',
        'tax_rate',
        'gross_amount',
        'part_id'
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
}

