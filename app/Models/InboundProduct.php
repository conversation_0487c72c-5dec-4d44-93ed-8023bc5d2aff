<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InboundProduct extends Model
{
    protected $table = 'inbound_products'; 

    protected $fillable = [
        'inbound_id',
        'product_id',
        'productNumber',
        'quantity',
    ];

    public function inbound()
    {
        return $this->belongsTo(Inbound::class);
    }
    
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
