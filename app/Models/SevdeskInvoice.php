<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;

class SevdeskInvoice extends Model
{
    use SoftDeletes;
    protected $table = 'sevdesk_invoices';

    protected $fillable = [
        'sevdesk_json',
        'sevdesk_response',
        'invoice_number',
        'invoice_contact_id',
        'status',
        'total',
        'is_template',
        'is_cancelled',
    ];

    protected $casts = [
        'sevdesk_json' => 'array',
        'sevdesk_response' => 'array',
        'is_template' => 'boolean'
    ];

    protected static function booted()
    {
        static::addGlobalScope('authorized', function (Builder $query) {
            if (auth()->check() && auth()->user()->role === 'user') {
                $query->whereHas('customer', function($q) {
                    $q->whereRaw('yetki_id = ? COLLATE utf8mb4_unicode_ci', [auth()->id()]);
                });
            }
        });
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'invoice_contact_id', 'client_id');
    }

    public function getInvoiceNumberFromResponse(): ?string
    {
        return $this->sevdesk_response['objects']['invoice']['invoiceNumber'] ?? null;
    }

    public function getTotalFromResponse()
    {
        if ($this->is_template == 1) {
            $positions = $this->sevdesk_json['invoicePosSave'] ?? [];

            return array_sum(array_map(function ($item) {
                $price = isset($item['price']) ? (float) $item['price'] : 0;
                $quantity = isset($item['quantity']) ? (int) $item['quantity'] : 1;
                $discount = isset($item['discount']) ? (float) $item['discount'] : 0;
                $taxRate = isset($item['taxRate']) ? (float) $item['taxRate'] : 0;

                // Apply discount before tax
                $netPrice = $price * (1 - ($discount / 100));
                $grossPerItem = $netPrice * (1 + ($taxRate / 100));
                $totalGross = $grossPerItem * $quantity;

                return $totalGross;
            }, $positions));
        }

        return (float) ($this->sevdesk_response['objects']['invoice']['sumGross'] ?? 0);
    }

    public function getStatusCodeFromResponse(): int
    {
        return (int) ($this->sevdesk_response['objects']['invoice']['status'] ?? 0);
    }

    public function getStatusLabelFromResponse(): string
    {
        if ($this->is_cancelled) {
            return 'Cancelled';
        }
        return match ($this->getStatusCodeFromResponse()) {
            50    => 'Deactivated recurring invoice',
            100   => 'Sent',
            200   => 'Open / Due',
            750   => 'Partially Paid',
            1000  => 'Paid',
            default => 'Unknown'
        };
    }

    public function getStatusBadgeClassFromResponse(): string
    {
        if ($this->is_cancelled) {
            return 'danger';
        }
        return match ($this->getStatusCodeFromResponse()) {
            50    => 'secondary',
            100   => 'warning',
            200   => 'info',
            750   => 'primary',
            1000  => 'success',
            default => 'dark'
        };
    }

}
