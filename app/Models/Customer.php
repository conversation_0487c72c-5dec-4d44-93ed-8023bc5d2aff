<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Customer extends Model
{
    protected $table = 'customers'; // tablo adı

    protected $fillable = [
        'client_id',
        'first_name',
        'last_name',
        'email',
        'phone',
        'company',
        'address',
        'house_number',
        'zip_code',
        'city',
        'country',
        'iban',
        'whatsapp_number',
        'notification_email',
        'whatsapp_permission',
        'email_permission',
        'yetki_id', // Carinin yetkilisinin ID'si
    ];
    
    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::addGlobalScope('authorized', function (Builder $query) {
            if (auth()->check() && auth()->user()->role === 'user') {
                $query->where('yetki_id', auth()->id());
            }
        });
        // add yetki kodu on create
        static::creating(function ($model) {
            if (auth()->check() && auth()->user()->role === 'user') {
                $model->yetki_id = auth()->id();
            }
        });
    }
    
    /**
     * Get the zip code from address.
     *
     * @return string
     */
    public function zipCodeAttribute()
    {
        if (empty($this->address)) {
            return '';
        }
        
        // Adres formatı: "Fritz-Werner-Str. 1, 12107 Berlin, Deutschland"
        $parts = explode(',', $this->address);
        
        if (count($parts) < 2) {
            return '';
        }
        
        // İkinci parçayı al (12107 Berlin)
        $cityPart = trim($parts[1]);
        
        // Boşluğa göre böl ve ilk kısmı al (12107)
        $zipCode = explode(' ', $cityPart)[0] ?? '';
        
        return $zipCode;
    }
    
    /**
     * Get the city from address.
     *
     * @return string
     */
    public function cityAttribute()
    {
        if (empty($this->address)) {
            return '';
        }
        
        // Adres formatı: "Fritz-Werner-Str. 1, 12107 Berlin, Deutschland"
        $parts = explode(',', $this->address);
        
        if (count($parts) < 2) {
            return '';
        }
        
        // İkinci parçayı al (12107 Berlin)
        $cityPart = trim($parts[1]);
        
        // Boşluğa göre böl ve ilk kısımdan sonraki kısmı al (Berlin)
        $cityParts = explode(' ', $cityPart);
        array_shift($cityParts); // İlk elemanı (zip code) çıkar
        
        return implode(' ', $cityParts);
    }

    public function countryAttribute()
    {
        if (empty($this->address)) {
            return '';
        }
        
        // Adres formatı: "Fritz-Werner-Str. 1, 12107 Berlin, Deutschland"
        $parts = explode(',', $this->address);
        
        if (count($parts) < 3) {
            return '';
        }
        
        // Üçüncü parçayı al (Deutschland)
        return trim($parts[2]);
    }

    /**
     * Get the ISO code for the country.
     *
     * @return string
     */
    public function getCountryIsoCodeAttribute()
    {
        if (empty($this->country)) {
            return 'DE'; // Default to Germany
        }
        
        // Country name to ISO code mapping
        $countryMap = [
            'Germany' => 'DE',
            'Deutschland' => 'DE',
            'Almanya' => 'DE',
            'France' => 'FR',
            'Frankreich' => 'FR',
            'Fransa' => 'FR',
            'Italy' => 'IT',
            'Italien' => 'IT',
            'İtalya' => 'IT',
            'Spain' => 'ES',
            'Spanien' => 'ES',
            'İspanya' => 'ES',
            'United Kingdom' => 'GB',
            'UK' => 'GB',
            'Great Britain' => 'GB',
            'England' => 'GB',
            'Vereinigtes Königreich' => 'GB',
            'Birleşik Krallık' => 'GB',
            'Netherlands' => 'NL',
            'The Netherlands' => 'NL',
            'Holland' => 'NL',
            'Niederlande' => 'NL',
            'Hollanda' => 'NL',
            'Belgium' => 'BE',
            'Belgien' => 'BE',
            'Belçika' => 'BE',
            'Switzerland' => 'CH',
            'Schweiz' => 'CH',
            'İsviçre' => 'CH',
            'Austria' => 'AT',
            'Österreich' => 'AT',
            'Avusturya' => 'AT',
            'Poland' => 'PL',
            'Polen' => 'PL',
            'Polonya' => 'PL',
            'Sweden' => 'SE',
            'Schweden' => 'SE',
            'İsveç' => 'SE',
            'Norway' => 'NO',
            'Norwegen' => 'NO',
            'Norveç' => 'NO',
            'Denmark' => 'DK',
            'Dänemark' => 'DK',
            'Danimarka' => 'DK',
            'Finland' => 'FI',
            'Finnland' => 'FI',
            'Finlandiya' => 'FI',
            'Turkey' => 'TR',
            'Türkiye' => 'TR',
            'Türkei' => 'TR',
            'USA' => 'US',
            'United States' => 'US',
            'United States of America' => 'US',
            'Vereinigte Staaten' => 'US',
            'Amerika Birleşik Devletleri' => 'US',
            'Canada' => 'CA',
            'Kanada' => 'CA',
            'Japan' => 'JP',
            'Japonya' => 'JP',
            'China' => 'CN',
            'Çin' => 'CN',
            'Australia' => 'AU',
            'Australien' => 'AU',
            'Avustralya' => 'AU',
            'Brazil' => 'BR',
            'Brasilien' => 'BR',
            'Brezilya' => 'BR',
            'India' => 'IN',
            'Indien' => 'IN',
            'Hindistan' => 'IN',
            'Russia' => 'RU',
            'Russland' => 'RU',
            'Rusya' => 'RU',
            'South Africa' => 'ZA',
            'Südafrika' => 'ZA',
            'Güney Afrika' => 'ZA',
            'Mexico' => 'MX',
            'Mexiko' => 'MX',
            'Meksika' => 'MX',
            'South Korea' => 'KR',
            'Südkorea' => 'KR',
            'Güney Kore' => 'KR',
        ];
        
        // Normalize country name (trim and convert to lowercase)
        $normalizedCountry = strtolower(trim($this->country));
        
        // Check for matching country code
        foreach ($countryMap as $countryName => $isoCode) {
            if (strtolower($countryName) === $normalizedCountry) {
                return $isoCode;
            }
        }
        
        // If no match is found, return DE as default
        return 'DE';
    }
    public function invoices()
    {
        return $this->hasMany(SevdeskInvoice::class, 'invoice_contact_id', 'client_id');
    }

    public function alternativeAddress()
    {
        return $this->hasOne(CustomerAlternativeAddress::class, 'customer_id', 'id');
    }

    /**
     * Get the authorized user for this customer.
     */
    public function authorized_user()
    {
        return $this->belongsTo(\App\Models\User::class, 'yetki_id');
    }
}
