<?php

namespace App\Models;

use App\Enums\InboundStatus;
use App\Enums\InboundType;
use Illuminate\Database\Eloquent\Model;

class Inbound extends Model
{
    protected $table = 'inbounds';
    
    protected $fillable = [
        'inbound_date',
        'invoice_number',
        'status',
        'type',
    ];

    protected $attributes = [
        'status' => 0, 
        'type' => 'new_stock',  
    ];

    public function products()
    {
        return $this->hasMany(InboundProduct::class, 'inbound_id');
    }
    
    public function getTotalQuantityAttribute()
    {
        return $this->products()->sum('quantity');
    }
    
    public function getStatusEnumAttribute(): InboundStatus
    {
        return InboundStatus::from($this->status);
    }
    
    public function getTypeEnumAttribute(): InboundType
    {
        return InboundType::from($this->type);
    }
}
