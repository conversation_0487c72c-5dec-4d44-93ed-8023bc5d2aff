<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Invoice extends Model
{
    protected $fillable = [
        'invoice_number',
        'invoice_date',
        'customer_id',
        'company_name',
        'address',
        'country',
        'portica_json',
        'portica_response',
        'approved_time',
        'iban',
        'total',
        'status',
        'notes'
    ];

    protected $attributes = [
        'status' => 0 // Varsayılan olarak pending
    ];

    const STATUS_PENDING = 0;
    const STATUS_APPROVED = 1;
    const STATUS_CANCELLED = 2;

    const STATUS_TEST = 99;

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::addGlobalScope('authorized', function (Builder $query) {
            if (auth()->check() && auth()->user()->role === 'user') {
                $query->whereHas('customer', function($q) {
                    $q->where('yetki_id', auth()->id());
                });
            }
        });
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }
    
    public function shipmentPackages()
    {
        return $this->hasMany(InvoiceShipmentPackage::class);
    }

    public function backlogIssues()
    {
        return $this->hasMany(BacklogList::class, 'invoice_number', 'invoice_number');
    }

    public function getStatusTextAttribute()
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_TEST => 'Test',
            default => 'Unknown'
        };
    }

    /**
     * Check if this invoice has unresolved issues in the backlog list
     * 
     * @return bool
     */
    public function getProblemAttribute()
    {
        return BacklogList::where('invoice_number', $this->invoice_number)
            ->where('resolved', 0)
            ->exists();
    }
}


