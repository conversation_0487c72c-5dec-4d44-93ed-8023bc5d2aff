<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class Product extends Model
{
    protected $fillable = ['product_json', 'brand'];

    public function getDecodedJsonAttribute()
    {
        return json_decode($this->product_json, true);
    }

    public static function syncWithApi($productData)
    {
        try {
            $credentials = 'YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6';
            //$productArray = [$productData];
            
            
            $response = Http::withHeaders([
                'Authorization' => 'Basic ' . $credentials,
                'Content-Type' => 'application/json',
                'Accept' => '*/*',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Connection' => 'keep-alive',
                'Host' => 'rest-api.portica.de',
                'User-Agent' => 'PostmanRuntime/7.29.0',
            ])->post('https://rest-api.portica.de/nurederm/api/product', $productData);

            if (!$response->successful()) {
                Log::error('API Sync Error', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
                throw new \Exception('API request failed: ' . $response->body());
            }

            return $response;
        } catch (\Exception $e) {
            Log::error('API Sync Error: ' . $e->getMessage());
            throw $e;
        }
    }
}





