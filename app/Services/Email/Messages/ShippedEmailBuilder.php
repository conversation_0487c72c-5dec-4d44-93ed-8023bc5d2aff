<?php

namespace App\Services\Email\Messages;

use App\Services\Email\EmailMessageBuilderInterface;

class ShippedEmailBuilder implements EmailMessageBuilderInterface
{
    public function build(array $data): array
    {
        $subject = "Ihre Bestellung {$data['invoice_number']} wurde versendet";
        $body = $this->formatMessage($data);

        return [
            'subject' => $subject,
            'body' => $body,
        ];
    }

    public function formatMessage(array $data): string
    {
        $packages = $data['packages'] ?? [];
        $customerId = $data['customer_id'] ?? null;
        if (empty($packages)) {
            return "Ihre Bestellung wurde an den Versanddienstleister übergeben. Die Sendungsinformationen werden bald verfügbar sein.";
        }

        $lines = ["Ihre Bestellung wurde an den Versanddienstleister übergeben."];

        foreach ($packages as $index => $pkg) {
            $num = $index + 1;
            $lines[] = "📦 Paket {$num}";
            $lines[] = "Bestellnummer: {$pkg['order_number']}";
            $lines[] = "Tracking-Link: {$pkg['tracking_link']}";
            $lines[] = "";
            if(!empty($customerId)) {
                $lines[] = "";
                $lines[] = "Um sich von unseren E-Mails abzumelden, besuchen Sie bitte diesen Link:\n";
                $lines[] = "https://portal.nurederm.de/email/unsubscribe/{$customerId}";
            }
        }

        return implode("\n", $lines);
    }
}
