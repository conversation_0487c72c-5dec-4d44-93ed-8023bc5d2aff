<?php

namespace App\Services\Email\Messages;

use App\Services\Email\EmailMessageBuilderInterface;

class InvoiceCreatedEmail implements EmailMessageBuilderInterface
{
    public function build(array $data): array
    {
        $subject = "Ihre Bestellung {$data['invoice_number']} wurde versendet";
        $body = $this->formatMessage($data);

        return [
            'subject' => $subject,
            'body' => $body,
        ];
    }

    public function formatMessage(array $data): string
    {
        $invoice_number = $data['invoice_number'] ?? 'unbekannt';
        $customer_id = $data['customer_id'] ?? null;
        
        $message = "Sehr geehrter Kunde,\n\n";
        $message .= "Ihre Bestellung mit der Nummer {$invoice_number} wurde versendet.\n\n";
        $message .= "Sie können den Status Ihrer Bestellung jederzeit in Ihrem Kundenkonto einsehen.\n\n";
        $message .= "Ihre Rechnung können Sie unter folgendem Link abrufen:\n";
        $message .= "https://portal.nurederm.de/invoices/{$invoice_number}.pdf\n\n";
        $message .= "Mit freundlichen Grüßen,\n";
        $message .= "Nurederm Deutschland\n\n";

        if($customer_id) {
            $message .= "Um sich von unseren E-Mails abzumelden, besuchen Sie bitte diesen Link:\n";
            $message .= "https://portal.nurederm.de/email/unsubscribe/{$customer_id}";
        }
        
        return $message;
    }
}
