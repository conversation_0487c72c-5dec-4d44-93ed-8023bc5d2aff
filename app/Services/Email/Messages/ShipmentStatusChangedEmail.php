<?php

namespace App\Services\Email\Messages;

use App\Services\Email\EmailMessageBuilderInterface;

class ShipmentStatusChangedEmail implements EmailMessageBuilderInterface
{
    public function build(array $data): array
    {
        $subject = "Aktualisierung zum Versandstatus Ihrer Bestellung {$data['invoice_number']}";

        $body = $this->formatMessage($data);

        return [
            'subject' => $subject,
            'body' => $body,
        ];
    }

    public function formatMessage(array $data): string
    {
        $packageNumber = $data['package_number'] ?? 'Unbekannt';
        $currentStatus = $data['current_status'] ?? 'Status nicht verfügbar';
        $customerId = $data['customer_id'] ?? null;

        $lines = [
            "📦 Paketnummer: {$packageNumber}",
            "",
            $currentStatus,
            ];

            if (!empty($data['tracking_service_url'])) {
                $lines[] = "";
                $lines[] = "Sie können den aktuellen Status Ihres Pakets jederzeit über den folgenden Link verfolgen:";
                $lines[] = $data['tracking_service_url'];
            }
            if(!empty($customerId)) {
                $lines[] = "";
                $lines[] = "Um sich von unseren E-Mails abzumelden, besuchen Sie bitte diesen Link:\n";
                $lines[] = "https://portal.nurederm.de/email/unsubscribe/{$customerId}";
            }

        return implode("\n", $lines);
    }
}
