<?php

namespace App\Services\Email;

use App\Services\Email\Messages\InvoiceCreatedEmail;
use App\Services\Email\Messages\ShipmentStatusChangedEmail;
use App\Services\Email\Messages\ShippedEmailBuilder;
use App\Services\Whatsapp\NotificationType;

class EmailMessageFactory
{
    public static function create(NotificationType $type): EmailMessageBuilderInterface
    {
        return match($type) {
            NotificationType::SHIPPED => new ShippedEmailBuilder(),
            NotificationType::INVOICE_CREATED => new InvoiceCreatedEmail(), 
            NotificationType::SHIPMENT_STATUS_CHANGED => new ShipmentStatusChangedEmail(), 
        };
    }
}
