<?php 
namespace App\Services\Whatsapp\Messages;
use App\Services\Whatsapp\WhatsappMessageBuilderInterface;
class ShippedMessage implements WhatsappMessageBuilderInterface
{
    public function build(string $phoneNumber, array $params): array
    {
        $text = $this->formatMessage($params['packages'] ?? []);
        $headerText = $params['invoice_number'] ?? 'N/A';

        return [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $phoneNumber,
            'type' => 'template',
            'template' => [
                'name' => 'portica_shipped', 
                'language' => [
                    'code' => 'de',
                ],
                'components' => [
                        [
                    'type' => 'header',
                    'parameters' => [
                        [
                            'type' => 'text',
                            'text' => $headerText
                        ]
                    ]
                        ],
                    [
                        'type' => 'body',
                           'parameters' => [
                        [
                            'type' => 'text',
                            'text' => $headerText
                        ],
                        [
                            'type' => 'text',
                            'text' => $text
                        ]
                    ]
                    ]
                ],
            ],
        ];
    }
    public function formatMessage(array $packages): string
    {
        if (empty($packages)) {
            return "Es liegen keine Tracking-Informationen vor.";
        }

        $messageLines = [];
        foreach ($packages as $index => $pkg) {
            $num = $index + 1;
            $messageLines[] = "📦 Paket {$num}";
            $messageLines[] = "Bestellnummer: {$pkg['order_number']}";
            $messageLines[] = "Tracking-Link: {$pkg['tracking_link']}";
        }
        $message = implode(" | ", $messageLines);

        $message = preg_replace('/\s{2,}/', ' ', $message);

        return $message;
    }

    public function formatHeaderText(array $invoice): string
    {
        if (empty($invoice)) {
            return "Kargonuz hazırlanıyor.";
        }

        return "Kargonuz gönderildi.";
    }
}