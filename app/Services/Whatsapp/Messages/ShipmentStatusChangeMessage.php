<?php 
namespace App\Services\Whatsapp\Messages;
use App\Services\Whatsapp\WhatsappMessageBuilderInterface;
class ShipmentStatusChangeMessage implements WhatsappMessageBuilderInterface
{
    public function build(string $phoneNumber, array $params): array
    {
        $text = $this->formatMessage($params);
        $headerText = $params['invoice_number'] ?? 'N/A';

        return [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $phoneNumber,
            'type' => 'template',
            'template' => [
                'name' => 'portica_shipment_status_updated',
                'language' => [
                    'code' => 'de',
                ],
                'components' => [
                    [
                        'type' => 'header',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'text' => $headerText
                            ]
                        ]
                    ],
                    [
                        'type' => 'body',
                        'parameters' => [
                            ['type' => 'text', 'text' => $text]
                        ]
                    ]
                ],
            ],
        ];
    }

     public function formatMessage(array $data): string
    {
        $packageNumber = $data['package_number'] ?? 'Unbekannt';
        $currentStatus = $data['current_status'] ?? 'Status nicht verfügbar';

        $lines = [
            "📦 Paketnummer: {$packageNumber}",
            $currentStatus,
        ];

        if (!empty($data['tracking_service_url'])) {
            $lines[] = "Sie können den aktuellen Status Ihres Pakets jederzeit über den folgenden Link verfolgen:";
            $lines[] = $data['tracking_service_url'];
        }

        $message = implode(" | ", $lines);

        $message = preg_replace('/[\r\n\t]+/', ' ', $message);
        $message = preg_replace('/\s{2,}/', ' ', $message);
        $message = trim($message);

        return $message;
    }
}