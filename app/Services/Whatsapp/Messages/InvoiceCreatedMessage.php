<?php

namespace App\Services\Whatsapp\Messages;

use App\Services\Whatsapp\WhatsappMessageBuilderInterface;

class InvoiceCreatedMessage implements WhatsappMessageBuilderInterface
{
    public function build(string $phoneNumber, array $params): array
    {
        $text = $this->formatMessage($params);
        $headerText = $params['invoice_number'] ?? 'N/A';

        return [
            'messaging_product' => 'whatsapp',
            'recipient_type' => 'individual',
            'to' => $phoneNumber,
            'type' => 'template',
            'template' => [
                'name' => 'portal_invoice_created',
                'language' => [
                    'code' => 'de',
                ],
                'components' => [
                    [
                        'type' => 'header',
                        'parameters' => [
                            [
                                'type' => 'text',
                                'text' => $headerText
                            ]
                        ]
                    ],
                    [
                        'type' => 'body',
                        'parameters' => [
                            ['type' => 'text', 'text' => $text]
                        ]
                    ]
                ],
            ],
        ];
    }
    public function formatMessage(array $data): string
    {
        $invoice_number = $data['invoice_number'];

        $messageLines = [];
        $messageLines[] = "Rechnungsnummer: {$invoice_number}";
        $messageLines[] = "Link: https://portal.nurederm.de/invoices/{$invoice_number}.pdf";

        $message = implode(" | ", $messageLines);

        // Remove extra whitespace
        $message = preg_replace('/\s{2,}/', ' ', $message);

        return $message;
    }
}
