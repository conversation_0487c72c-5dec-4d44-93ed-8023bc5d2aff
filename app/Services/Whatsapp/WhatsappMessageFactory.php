<?php 
namespace App\Services\Whatsapp;

use App\Services\Whatsapp\Messages\InvoiceCreatedMessage;
use App\Services\Whatsapp\Messages\ShipmentStatusChangeMessage;
use App\Services\Whatsapp\Messages\ShippedMessage;
use App\Services\Whatsapp\WhatsappMessageBuilderInterface;

class WhatsappMessageFactory
{
    public static function create(NotificationType $type): WhatsappMessageBuilderInterface
    {
        return match($type) {
            NotificationType::INVOICE_CREATED => new InvoiceCreatedMessage(),
            NotificationType::SHIPPED => new ShippedMessage(),
            NotificationType::SHIPMENT_STATUS_CHANGED => new ShipmentStatusChangeMessage(), 
        };
    }
}
