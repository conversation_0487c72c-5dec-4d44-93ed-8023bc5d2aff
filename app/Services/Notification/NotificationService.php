<?php

namespace App\Services\Notification;

use App\Models\Invoice;
use App\Models\MessageLog;
use App\Services\Email\EmailMessageFactory;
use App\Services\Whatsapp\NotificationType;
use App\Services\Whatsapp\WhatsappMessageFactory;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class NotificationService
{
    /**
     * Send notifications to user via WhatsApp and email
     */
    public function notifyUser(Invoice $invoice, NotificationType $type, array $messageData)
    {
        $customer = $invoice->customer;

        if ($customer && !empty($customer->whatsapp_number) && ($customer->whatsapp_permission === null || $customer->whatsapp_permission === true || $customer->whatsapp_permission === 1)){
            $this->sendWhatsAppNotification($customer->whatsapp_number, $type, $messageData);
        }
        if($customer && !empty($customer->notification_email) && ($customer->email_permission === null || $customer->email_permission === true || $customer->email_permission === 1)) {
            $this->sendEmailNotification($customer, $type, $messageData);
        } 
        
    }

    /**
     * Send WhatsApp notification
     */
    private function sendWhatsAppNotification(string $phoneNumber, NotificationType $type, array $messageData)
    {
        try{
            $builder = WhatsappMessageFactory::create($type);
            $body = $builder->build($phoneNumber, $messageData);
            
            $response = Http::withToken(env('WHATSAPP_TOKEN'))->post(
                'https://graph.facebook.com/v21.0/614040155127423/messages',
                $body
            );
            if (!$response->successful()) {
                Log::error("Failed to send WhatsApp message to {$phoneNumber}: " . $response->body());
            }
            MessageLog::create([
                'data' => json_encode([
                    'status' => $response->successful() ? 'sent' : 'failed',
                    'to' => $phoneNumber,
                    'message' => $body
                ])
            ]);
        }
        catch (\Exception $e) {
            Log::error("Error sending WhatsApp message to {$phoneNumber}: " . $e->getMessage());
            MessageLog::create([
                'data' => json_encode([
                    'status' => 'error',
                    'to' => $phoneNumber,
                    'message' => $e->getMessage()
                ])
            ]);
        }
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification($customer, NotificationType $type, array $messageData)
    {
        try{
            $emailBuilder = EmailMessageFactory::create($type);
            $emailContent = $emailBuilder->build($messageData);

            Mail::raw($emailContent['body'], function ($message) use ($customer, $emailContent) {
                $message->to($customer->notification_email)
                        ->subject($emailContent['subject']);
            });

            MessageLog::create([
                'data' => json_encode([
                    'status' => 'sent',
                    'to' => $customer->notification_email,
                'subject' => $emailContent['subject'],
                'body' => $emailContent['body']
            ])
        ]);
    }
        catch (\Exception $e) {
            MessageLog::create([
                'data' => json_encode([
                    'status' => 'error',
                    'to' => $customer->notification_email,
                    'message' => $e->getMessage()
                ])
            ]);
        }
    }
}