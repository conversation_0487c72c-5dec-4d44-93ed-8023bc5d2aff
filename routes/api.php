<?php

use App\Http\Controllers\Api\InvoiceController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Http;
use App\Http\Controllers\ProductController;

Route::middleware('api.key')->group(function () {
    Route::post('/invoice/create', [InvoiceController::class, 'create']);
    Route::post('/products-bulk', [ProductController::class, 'storeTotal'])->name('product.storeTotal');
    Route::post('/portica-daily', [ProductController::class, 'porticaDaily'])->name('product.porticaDaily');
    
    // Yeni stock endpoint'i
    Route::get('/stock/{productId?}', function ($productId = null) {
        $url = 'https://rest-api.portica.de/nurederm/api/stock';

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
                'Content-Type' => 'application/json',
                'Accept' => '*/*',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Connection' => 'keep-alive',
                'User-Agent' => 'PostmanRuntime/7.29.0',
                'Host' => 'rest-api.portica.de'
            ])->get($url);

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch stock data',
                    'error' => $response->body()
                ], $response->status());
            }

            return response()->json([
                'success' => true,
                'data' => $response->json()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
                'error' => $e->getMessage()
            ], 500);
        }
    });

});

// Ürün fotoğrafları için API endpoint'i
Route::get('/products-portal/{id}', function($id) {
    try {
        $response = Http::withHeaders([
            'token' => '1ce5a3b9195c2113acd418a0e36c6d554d457f76340e4af82eb3ddfb',
        ])->get('https://www.nurederm.com/products-portal/' . $id);

        if (!$response->successful()) {
            return response()->json([
                'error' => 'Failed to fetch product data'
            ], $response->status());
        }

        return response()->json($response->json());
    } catch (\Exception $e) {
        return response()->json([
            'error' => $e->getMessage()
        ], 500);
    }
});


