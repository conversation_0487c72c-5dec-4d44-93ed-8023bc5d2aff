
<?php

use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\ProductController;
use Illuminate\Support\Facades\Http;
use App\Models\Product;
use App\Http\Controllers\UserController;
use App\Helpers\FtpHelper;
use App\Http\Controllers\InboundController;
use App\Http\Controllers\OrderCargoStatusController;
use App\Http\Controllers\StatisticsController;
use App\Http\Controllers\SevdeskInvoiceController;

// Auth routes
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/logout', [AuthController::class, 'logout'])->name('logout');


Route::post('/test', function (Request $request) {
    error_log('Test route accessed');
    error_log('Request data: ' . json_encode($request->all()));
    return response()->json(['message' => 'Test route accessed'])
        ->header('Access-Control-Allow-Origin', '*')
        ->header('Access-Control-Allow-Methods', 'POST, OPTIONS')
        ->header('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With');
});
// Protected routes
Route::middleware('auth')->group(function () {
    Route::get('/', [HomeController::class, 'index'])->name('home.index');
    
    Route::get('/order/search', [OrderController::class, 'search'])->name('order.search');
    Route::get('/order', [InvoiceController::class, 'index'])->name('order.index');
    Route::get('/order/{id}', [InvoiceController::class, 'show'])->name('order.details');
    Route::get('/order/{id}/edit', [InvoiceController::class, 'edit'])->name('order.edit');
    Route::delete('/order/{id}', [InvoiceController::class, 'destroy'])->name('order.destroy');
    Route::post('/order', [InvoiceController::class, 'store'])->name('order.store');
    Route::post('/order/{id}/status', [InvoiceController::class, 'updateStatus'])->name('order.status.update');
    Route::post('/order/{id}', [InvoiceController::class, 'update'])->name('order.update');
    Route::post('/order/{id}/update-details', [OrderController::class, 'updateOrderDetails'])->name('order.update.details');
    Route::post('/order/{id}/approve', [OrderController::class, 'approve'])->name('order.approve');
    Route::post('/order/{id}/reset', [OrderController::class, 'reset'])->name('order.status.reset');
    Route::post('/order/{id}/cancel', [OrderController::class, 'cancelOrder'])->name('order.status.cancel');
    Route::get('/customer/{customerId}/alternative-address', [OrderController::class, 'getCustomerAlternativeAddress'])->name('customer.alternative.address');

    Route::get('/customers', [CustomerController::class, 'index'])->name('customer.index');
    Route::get('/customer/{id}/edit', [CustomerController::class, 'edit'])->name('customer.edit');
    Route::put('/customer/{id}', [CustomerController::class, 'update'])->name('customer.update');
    Route::post('/customer', [CustomerController::class, 'store'])->name('customer.store');
    Route::delete('/customer/{id}', [CustomerController::class, 'destroy'])->name('customer.destroy');
    Route::post('/customer/assign-user', [CustomerController::class, 'assignUser'])->name('customer.assign-user');
    Route::get('/customer/communication-ways', [CustomerController::class, 'getCommunicationWays'])->name('customer.communication-ways');
    

    Route::get('/products', [ProductController::class, 'index'])->name('product.index');
    Route::post('/products', [ProductController::class, 'store'])->name('product.store');
    Route::post('/products/update/{id}', [ProductController::class, 'update'])->name('product.update');
    Route::post('/products/check-item-number', [ProductController::class, 'checkItemNumber'])->name('product.checkItemNumber');
    Route::get('/product/search', [ProductController::class, 'search'])->name('product.search');

    // User routes
    Route::middleware('admin')->group(function () {
        Route::get('/users', [UserController::class, 'index'])->name('user.index');
    });
    Route::post('/user', [UserController::class, 'store'])->name('user.store');
    Route::put('/user/{id}', [UserController::class, 'update'])->name('user.update');
    Route::delete('/user/{id}', [UserController::class, 'destroy'])->name('user.destroy');

    Route::get('/inbounds', [InboundController::class, 'index'])->name('inbound.index');
    Route::post('/inbound', [InboundController::class, 'store'])->name('inbound.store');
    Route::get('/inbound/{id}/edit', [InboundController::class, 'edit'])->name('inbound.edit');
    Route::put('/inbound/{id}', [InboundController::class, 'update'])->name('inbound.update');
    Route::delete('/inbound/{id}', [InboundController::class, 'destroy'])->name('inbound.destroy');
    Route::get('/inbound/create', [InboundController::class, 'create'])->name('inbound.create');
    Route::post('/inbound/store', [InboundController::class, 'store'])->name('store');
    Route::post('/inbound/find-products', [InboundController::class, 'findProducts'])->name('inbound.find-products');

    Route::get('/old-invoices', [InvoiceController::class, 'oldInvoices'])->name('invoice.old');

    Route::get('/statistics', [StatisticsController::class, 'index'])->name('statistics.index');

    // Sevdesk Invoice Routes
    Route::get('/sevdesk', [SevdeskInvoiceController::class, 'index'])->name('sevdesk.index');
    Route::get('/sevdesk/create', [SevdeskInvoiceController::class, 'create'])->name('sevdesk.create');
    Route::get('/sevdesk/search', [SevdeskInvoiceController::class, 'search'])->name('sevdesk.search');
    Route::post('/sevdesk/store', [SevdeskInvoiceController::class, 'store'])->name('sevdesk.store');
    Route::get('/sevdesk/template/{id}', [SevdeskInvoiceController::class, 'useTemplate'])->name('sevdesk.template');

    Route::get('/sevdesk/{id}', [SevdeskInvoiceController::class, 'show'])->name('sevdesk.show');
    Route::delete('/sevdesk/{id}/cancel', [SevdeskInvoiceController::class, 'cancel'])->name('sevdesk.cancel');
    Route::delete('/sevdesk/{id}/destroy', [SevdeskInvoiceController::class, 'destroy'])->name('sevdesk.destroy');
});

Route::get('/email/unsubscribe/{customerId}', [CustomerController::class, 'unsubscribe'])->name('customer.unsubscribe');

Route::get('/api/stock-proxy', function () {
    $response = Http::withHeaders([
        'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
        'Content-Type' => 'application/json',
        'Accept' => '*/*',
        'Accept-Encoding' => 'gzip, deflate, br',
        'Connection' => 'keep-alive',
        'User-Agent' => 'PostmanRuntime/7.29.0',
        'Host' => 'rest-api.portica.de'
    ])->get('https://rest-api.portica.de/nurederm/api/stock');

    return response()->json($response->json());
});


Route::get('/api/api-stock-local-check', function () {
    $response = Http::withHeaders([
        'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
        'Content-Type' => 'application/json',
        'Accept' => '*/*',
        'Accept-Encoding' => 'gzip, deflate, br',
        'Connection' => 'keep-alive',
        'User-Agent' => 'PostmanRuntime/7.29.0',
        'Host' => 'rest-api.portica.de'
    ])->get('https://rest-api.portica.de/nurederm/api/stock');

    $responseBody = $response->json();
    $products = Product::all();

    foreach ($products as $product) {
        $productData = json_decode($product->product_json, true);
        $itemNumber = $productData[0]['itemNumber'] ?? null;

        if ($itemNumber) {
            $apiData = collect($responseBody['data']);
            $stockData = $apiData->firstWhere('productNumber', $itemNumber);

            if ($stockData) {
                echo 'Item Number: ' . $itemNumber . ', Stock: ' . $stockData['stock'] . "<br>";
            } else {
                echo 'Item Number: ' . $itemNumber . ' not found in stock data' . "<br>";
            }
        } else {
            echo 'Item Number not found for product ID: ' . $product->id . "<br>";
        }

        
    }
    

});


Route::get('/api/order-status/{orderNumber}', function ($orderNumber) {
        $url = "https://rest-api.portica.de/nurederm/api/order/{$orderNumber}";

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Basic YXBpX3VzZXJfZXh0ZXJuOmV1ZjM5ZTIkZWt3T3AyMXV6',
                'Content-Type' => 'application/json',
                'Accept' => '*/*',
                'Accept-Encoding' => 'gzip, deflate, br',
                'Connection' => 'keep-alive',
                'User-Agent' => 'PostmanRuntime/7.29.0',
                'Host' => 'rest-api.portica.de'
            ])->get($url);

            if (!$response->successful()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch order status',
                    'error' => $response->body()
                ], $response->status());
            }

            return response()->json([
                'success' => true,
                'data' => $response->json()['data'] ?? null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching order status: ' . $e->getMessage()
            ], 500);
        }
    });
Route::get('/update-order-statuses', [OrderCargoStatusController::class, 'updateShipmentStatuses'])->name('cronjob.updateShipmentStatuses');
Route::get('/track-shipment-statuses/{token}', [OrderCargoStatusController::class, 'trackShipmentStatus'])->name('cronjob.updateShipmentStatuses');
Route::get('/fetch-stock-data/{token}', [OrderController::class, 'fetchStockData'])->name('cronjob.fetchStockData');
Route::get('/check-returned-products/{token}', [InboundController::class, 'checkReturnedProducts'])->name('cronjob.checkReturnedProducts');

Route::get('/receive-webhook', [CustomerController::class, 'receiveWebhook'])->name('customer.receiveWebhook');
// FTP upload test route
// Route::get('/test-ftp-upload/{filename}', function ($filename) {
//     $result = FtpHelper::uploadInvoicePdf($filename);
//     return response()->json([
//         'success' => $result,
//         'message' => $result ? 'Dosya başarıyla yüklendi' : 'Dosya yüklenirken hata oluştu'
//     ]);

// });

// Ürün arşivleme rotaları
Route::post('/product/{id}/archive', [ProductController::class, 'archive'])->name('product.archive');
Route::get('/product-archives', [ProductController::class, 'showArchives'])->name('product.archives');
Route::post('/product-archives/{id}/restore', [ProductController::class, 'restoreFromArchive'])->name('product.restore');



// // Test notification route
// Route::get('/test-notification/{invoice_id}', function ($invoice_id) {
//     try {
//         $invoice = Invoice::where('invoice_number', $invoice_id)->firstOrFail();
//         $notificationService = app(NotificationService::class);
        
//         $notificationService->notifyUser(
//             $invoice,
//             App\Services\Whatsapp\NotificationType::INVOICE_CREATED,
//             ['invoice_number' => $invoice->invoice_number]
//         );
        
//         return response()->json([
//             'success' => true,
//             'message' => "Notification sent for invoice #{$invoice->invoice_number}",
//         ]);
//     } catch (\Exception $e) {
//         return response()->json([
//             'success' => false,
//             'message' => 'Failed to send notification',
//             'error' => $e->getMessage()
//         ], 500);
//     }
// })->name('test.notification');

// Inbound routes

// Sevdesk Invoice Ro
// Customer user assignment route

Route::post('/backlog/resolve', [App\Http\Controllers\BacklogController::class, 'resolve'])->name('backlog.resolve');


